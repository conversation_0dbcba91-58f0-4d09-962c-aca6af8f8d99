import 'package:account_management/application/period_tracking_watcher_bloc/period_tracking_watcher_bloc.dart';
import 'package:account_management/account_management.dart';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../custom_widgets/rounded_checkbox.dart';
import '../../custom_widgets/curved_app_bar.dart';
import '../../helpers.dart';
import 'package:design_system/design_system.dart';

@RoutePage()
class PeriodTrackingEditPage extends StatelessWidget {
  final int? initialYear;

  const PeriodTrackingEditPage({super.key, this.initialYear});

  @override
  Widget build(BuildContext context) {
    final yearToUse = initialYear ?? DateTime.now().year;
    print(
        'PeriodTrackingEditPage: initialYear = $initialYear, yearToUse = $yearToUse');

    return MultiBlocProvider(
      providers: [
        BlocProvider<PeriodTrackingWatcherBloc>(create: (context) {
          final bloc = getIt<PeriodTrackingWatcherBloc>();
          print('PeriodTrackingEditPage: Starting bloc with year $yearToUse');
          bloc.add(PeriodTrackingWatcherEvent.watchYearStarted(yearToUse));
          return bloc;
        }),
        BlocProvider<ManagePeriodTrackingBloc>(
          create: (context) => getIt<ManagePeriodTrackingBloc>(),
        ),
      ],
      child: PeriodTrackingEditScaffold(initialYear: yearToUse),
    );
  }
}

class PeriodTrackingEditScaffold extends StatefulWidget {
  final int initialYear;

  const PeriodTrackingEditScaffold({required this.initialYear, super.key});

  @override
  State<PeriodTrackingEditScaffold> createState() =>
      _PeriodTrackingEditScaffoldState();
}

class _PeriodTrackingEditScaffoldState
    extends State<PeriodTrackingEditScaffold> {
  // CRITICAL: Track locally selected dates for batch operations
  Set<DateTime> _localSelectedDates = {};
  Set<DateTime> _originalSelectedDates =
      {}; // Track original state from database
  late int _selectedYear;
  bool _isInitialized = false; // Prevent reinitialization during editing

  @override
  void initState() {
    super.initState();
    _selectedYear = widget.initialYear;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: CurvedAppBar(
        appBarColor: AppTheme.primaryColor,
        logoColor: const Color(0xffFAF2DF),
        height: .35.sw,
        subtitle: 'Insights',
        subtitleColor: const Color(0xffFAF2DF),
        topLeftIcon: Container(
          height: 40,
          width: 40,
          decoration: BoxDecoration(
            color: const Color(0xffFAF2DF),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back,
                color: AppTheme.primaryColor, size: 20),
            onPressed: () {
              context.router.pop();
            },
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xffFBF0D5), // Light cream
              Color(0xffF8EEFF), // Light purple
            ],
          ),
        ),
        child:
            BlocBuilder<PeriodTrackingWatcherBloc, PeriodTrackingWatcherState>(
          builder: (context, state) {
            return state.maybeMap(
              loading: (_) {
                return Padding(
                  padding: EdgeInsets.only(top: .35.sw),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(
                          color: AppTheme.primaryColor,
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'Loading period data...',
                          style: GoogleFonts.roboto(
                            color: AppTheme.primaryColor,
                            fontSize: 16.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
              loadSuccess: (dataState) {
                // Extract period dates from the year data
                final periodDates = <DateTime>{};
                for (final monthEntry in dataState.yearData.entries) {
                  for (final dayEntry in monthEntry.value.entries) {
                    final periodModel = dayEntry.value;
                    if (periodModel.isPeriodDate == true &&
                        periodModel.date != null) {
                      periodDates.add(periodModel.date!);
                    }
                  }
                }

                // CRITICAL: Initialize local selected dates with period dates only once
                if (!_isInitialized) {
                  _localSelectedDates = Set.from(periodDates);
                  _originalSelectedDates =
                      Set.from(periodDates); // Track original state
                  _isInitialized = true;
                }

                return Padding(
                  padding: EdgeInsets.only(top: .35.sw),
                  child: _buildCalendarView(),
                );
              },
              orElse: () => Padding(
                padding: EdgeInsets.only(top: .35.sw),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.calendar_today,
                          size: 64.w, color: Colors.grey),
                      SizedBox(height: 16.h),
                      Text(
                        'No period data found',
                        style: GoogleFonts.roboto(
                          fontSize: 18.sp,
                          color: Colors.grey,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Start by selecting some dates',
                        style: GoogleFonts.roboto(
                          fontSize: 14.sp,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
      bottomNavigationBar: _buildDoneButton(),
    );
  }

  Widget _buildCalendarView() {
    return Column(
      children: [
        // Scrollable calendar months
        Expanded(
          child: _buildScrollableCalendar(),
        ),
        // Done button
      ],
    );
  }

  Widget _buildScrollableCalendar() {
    final months =
        List.generate(12, (index) => DateTime(_selectedYear, index + 1, 1));

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      itemCount: months.length,
      itemBuilder: (context, index) {
        return _buildMonthCard(months[index]);
      },
    );
  }

  Widget _buildMonthCard(DateTime month) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 6),
      child: Container(
        margin: EdgeInsets.only(bottom: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(32),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // Month title
              Text(
                _getMonthYearText(month),
                style: GoogleFonts.roboto(
                  fontSize: 25.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.primaryColor,
                ),
              ),
              SizedBox(height: 12.h),
              // Days of week header
              _buildDaysOfWeekHeader(),
              // Calendar grid for this month
              _buildMonthCalendarGrid(month),
            ],
          ),
        ),
      ),
    );
  }

  // Days of week header
  Widget _buildDaysOfWeekHeader() {
    final daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return Row(
      children: daysOfWeek
          .map((day) => Expanded(
                child: Center(
                  child: Text(
                    day,
                    style: GoogleFonts.roboto(
                      fontSize: 25.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildMonthCalendarGrid(DateTime month) {
    final daysInMonth = _getDaysInMonth(month);
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7; // Sunday = 0

    // Build calendar using Column and Row for better control
    List<Widget> weeks = [];
    List<Widget> currentWeek = [];

    // Add empty cells for days before the first day of the month
    for (int i = 0; i < firstWeekday; i++) {
      currentWeek.add(Expanded(child: Container()));
    }

    // Add days of the month
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      currentWeek.add(Expanded(child: _buildDateCell(date)));

      // If we've filled a week (7 days), start a new week
      if (currentWeek.length == 7) {
        weeks.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 2.h),
            child: Row(children: List.from(currentWeek)),
          ),
        );
        currentWeek.clear();
      }
    }

    // Fill remaining cells in the last week
    while (currentWeek.length < 7) {
      currentWeek.add(Expanded(child: Container()));
    }
    if (currentWeek.isNotEmpty) {
      weeks.add(
        Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h),
          child: Row(children: List.from(currentWeek)),
        ),
      );
    }

    return Column(
      children: weeks,
    );
  }

  Widget _buildDateCell(DateTime date) {
    // Inline the widget creation with proper styling and functionality
    final today = DateTime.now();
    final todayNormalized = DateTime(today.year, today.month, today.day);
    final dateNormalized = DateTime(date.year, date.month, date.day);
    final januaryFirst = DateTime(_selectedYear, 1, 1);

    // Check date properties
    bool isTodayDate = dateNormalized.isAtSameMomentAs(todayNormalized);
    bool isFutureDate = dateNormalized.isAfter(todayNormalized);
    bool isSelectableDate = dateNormalized
            .isAfter(januaryFirst.subtract(const Duration(days: 1))) &&
        !isFutureDate;

    // CRITICAL: Check if this date is selected in local state
    bool isSelectedPeriodDate = _localSelectedDates.any((selectedDay) =>
        selectedDay.year == date.year &&
        selectedDay.month == date.month &&
        selectedDay.day == date.day);

    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Day number
          Text(
            '${date.day}',
            style: GoogleFonts.roboto(
              color:
                  isFutureDate ? Colors.grey.shade400 : AppTheme.primaryColor,
              fontWeight: FontWeight.w500,
              fontSize: 25.sp,
            ),
          ),
          SizedBox(height: 2.h),
          // Checkbox for selectable dates
          if (isSelectableDate)
            RoundedCheckbox(
              isChecked: isSelectedPeriodDate,
              size: 50.0,
              checkedColor: AppTheme.primaryColor,
              uncheckedColor: Colors.transparent,
              onTap: () {
                // CRITICAL: Toggle date selection in local state
                setState(() {
                  final normalizedDate =
                      DateTime(date.year, date.month, date.day);
                  if (isSelectedPeriodDate) {
                    _localSelectedDates.remove(normalizedDate);
                  } else {
                    _localSelectedDates.add(normalizedDate);
                  }
                });
              },
            )
          else
            SizedBox(height: 20.0), // Maintain spacing for non-selectable dates
          // Today indicator
          if (isTodayDate)
            Container(
              width: 20.w,
              height: 2.h,
              margin: EdgeInsets.only(top: 1.h),
              decoration: BoxDecoration(
                color: const Color(0xFFE91E63),
                borderRadius: BorderRadius.circular(1.r),
              ),
            ),
        ],
      ),
    );
  }

  // CRITICAL: Done button with ovulation calculation logic
  Widget _buildDoneButton() {
    return BlocListener<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
      listener: (context, state) {
        state.maybeWhen(
          success: () {
            // Show success message and navigate back
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Period dates saved successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.of(context).pop();
          },
          failure: (failure) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to save period dates. Please try again.'),
                backgroundColor: Colors.red,
              ),
            );
          },
          orElse: () {},
        );
      },
      child: BlocBuilder<ManagePeriodTrackingBloc, ManagePeriodTrackingState>(
        builder: (context, state) {
          final isLoading = state.maybeWhen(
            loading: () => true,
            orElse: () => false,
          );

          return GestureDetector(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xffF8EEFF), // Light pu,
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 25.0, vertical: 15.0),
                child: Container(
                  height: .13.sw,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(32),
                  ),
                  alignment: Alignment.center,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    child: isLoading
                        ? SizedBox(
                            width: 20.w,
                            height: 20.h,
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            'Done',
                            style: GoogleFonts.roboto(
                              color: Colors.white,
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),
              ),
            ),
            onTap: isLoading
                ? null
                : () async {
                    // Calculate newly selected and deselected dates
                    final newlySelected =
                        _localSelectedDates.difference(_originalSelectedDates);
                    final newlyDeselected =
                        _originalSelectedDates.difference(_localSelectedDates);

                    final bloc = context.read<ManagePeriodTrackingBloc>();

                    // If no changes, just close the page
                    if (newlySelected.isEmpty && newlyDeselected.isEmpty) {
                      Navigator.of(context).pop();
                      return;
                    }

                    // STEP 1: Handle period date changes
                    if (newlySelected.isNotEmpty) {
                      bloc.add(ManagePeriodTrackingEvent.selectPeriodDates(
                          newlySelected));
                    }

                    if (newlyDeselected.isNotEmpty) {
                      bloc.add(ManagePeriodTrackingEvent.deselectPeriodDates(
                          newlyDeselected));
                    }

                    // STEP 2: Calculate ovulation for affected cycles (handles both selected and deselected)
                    bloc.add(ManagePeriodTrackingEvent
                        .calculateOvulationForAffectedCycles(
                      newlySelected: newlySelected,
                      newlyDeselected: newlyDeselected,
                    ));
                  },
          );
        },
      ),
    );
  }

  // Helper methods
  String _getMonthYearText(DateTime date) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  int _getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }
}
