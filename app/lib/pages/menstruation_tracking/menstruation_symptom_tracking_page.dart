import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';
import 'package:intl/intl.dart';

import 'package:account_management/application/menstruation_tracking/menstruation_tracking_bloc.dart';
import 'package:account_management/application/menstruation_tracking/menstruation_tracking_event.dart';
import 'package:account_management/application/menstruation_tracking/menstruation_tracking_state.dart';
import 'package:account_management/domain/model/symptom_model.dart';

@RoutePage()
class MenstruationSymptomTrackingPage extends StatefulWidget {
  final DateTime selectedDate;

  const MenstruationSymptomTrackingPage({
    required this.selectedDate,
    Key? key,
  }) : super(key: key);

  @override
  State<MenstruationSymptomTrackingPage> createState() =>
      _MenstruationSymptomTrackingPageState();
}

class _MenstruationSymptomTrackingPageState
    extends State<MenstruationSymptomTrackingPage> {
  final List<SymptomModel> _availableSymptoms = [
    SymptomModel(name: 'Headache'),
    SymptomModel(name: 'Fatigue'),
    SymptomModel(name: 'Bloating'),
    SymptomModel(name: 'Back Pain'),
    SymptomModel(name: 'Cramps'),
    SymptomModel(name: 'Breakouts'),
    SymptomModel(name: 'Mood Swings'),
    SymptomModel(name: 'Breast Tenderness'),
    SymptomModel(name: 'Nausea'),
    SymptomModel(name: 'Diarrhea'),
    SymptomModel(name: 'Constipation'),
    SymptomModel(name: 'Food Cravings'),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFAF2DF),
      appBar: AppBar(
        backgroundColor: Color(0xff30285D),
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.router.pop(),
          icon: Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24.sp,
          ),
        ),
        title: Text(
          DateFormat('MMMM dd, yyyy').format(widget.selectedDate),
          style: GoogleFonts.roboto(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
      body: BlocBuilder<MenstruationTrackingBloc, MenstruationTrackingState>(
        builder: (context, state) {
          return state.maybeMap(
            loaded: (loadedState) => _buildContent(context, loadedState),
            orElse: () => Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xff30285D)),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    dynamic state,
  ) {
    final selectedDayData = state.selectedDayData;
    final currentSymptoms =
        (selectedDayData?.symptoms as List<SymptomModel>?) ?? <SymptomModel>[];
    final painLevel = (selectedDayData?.painLevel as int?) ?? 0;
    final flowLevel = (selectedDayData?.flowLevel as int?) ?? 0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pain Level Section
          _buildSectionCard(
            title: 'Pain Level',
            icon: Icons.sentiment_very_dissatisfied,
            child: _buildPainLevelSelector(context, painLevel),
          ),
          SizedBox(height: 16.h),

          // Flow Level Section
          _buildSectionCard(
            title: 'Flow Level',
            icon: Icons.water_drop,
            child: _buildFlowLevelSelector(context, flowLevel),
          ),
          SizedBox(height: 16.h),

          // Symptoms Section
          _buildSectionCard(
            title: 'Symptoms',
            icon: Icons.health_and_safety,
            child: _buildSymptomsSelector(context, currentSymptoms),
          ),
          SizedBox(height: 32.h),

          // Save Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Save all data and go back
                context.router.pop();
              },
              child: Text(
                'Save Changes',
                style: GoogleFonts.roboto(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xff30285D),
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8.r,
            offset: Offset(0, 2.h),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Color(0xff30285D),
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                title,
                style: GoogleFonts.roboto(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Color(0xff30285D),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          child,
        ],
      ),
    );
  }

  Widget _buildPainLevelSelector(BuildContext context, int currentLevel) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'No Pain',
              style: GoogleFonts.roboto(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: Color(0xff666666),
              ),
            ),
            Text(
              'Severe Pain',
              style: GoogleFonts.roboto(
                fontSize: 12.sp,
                fontWeight: FontWeight.w400,
                color: Color(0xff666666),
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(5, (index) {
            final level = index + 1;
            final isSelected = currentLevel == level;

            return GestureDetector(
              onTap: () {
                context.read<MenstruationTrackingBloc>().add(
                      MenstruationTrackingEvent.painLevelUpdated(
                        date: widget.selectedDate,
                        painLevel: level,
                      ),
                    );
              },
              child: Container(
                width: 50.w,
                height: 50.h,
                decoration: BoxDecoration(
                  color: isSelected ? Color(0xffE91E63) : Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    level.toString(),
                    style: GoogleFonts.roboto(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? Colors.white : Color(0xff666666),
                    ),
                  ),
                ),
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildFlowLevelSelector(BuildContext context, int currentLevel) {
    final flowLabels = ['Light', 'Normal', 'Heavy', 'Very Heavy'];

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(4, (index) {
            final level = index + 1;
            final isSelected = currentLevel == level;

            return GestureDetector(
              onTap: () {
                context.read<MenstruationTrackingBloc>().add(
                      MenstruationTrackingEvent.flowLevelUpdated(
                        date: widget.selectedDate,
                        flowLevel: level,
                      ),
                    );
              },
              child: Column(
                children: [
                  Container(
                    width: 60.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: isSelected ? Color(0xffE91E63) : Colors.grey[200],
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.water_drop,
                        color: isSelected ? Colors.white : Color(0xff666666),
                        size: 20.sp,
                      ),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    flowLabels[index],
                    style: GoogleFonts.roboto(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400,
                      color: isSelected ? Color(0xff30285D) : Color(0xff666666),
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildSymptomsSelector(
      BuildContext context, List<SymptomModel> currentSymptoms) {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: _availableSymptoms.length,
      itemBuilder: (context, index) {
        final symptom = _availableSymptoms[index];
        final isSelected = currentSymptoms.any((s) => s.name == symptom.name);

        return GestureDetector(
          onTap: () {
            context.read<MenstruationTrackingBloc>().add(
                  MenstruationTrackingEvent.symptomToggled(
                    date: widget.selectedDate,
                    symptom: symptom,
                  ),
                );
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: isSelected ? Color(0xff30285D) : Colors.grey[100],
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(
                color: isSelected ? Color(0xff30285D) : Colors.grey[300]!,
                width: 1.w,
              ),
            ),
            child: Center(
              child: Text(
                symptom.name,
                style: GoogleFonts.roboto(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: isSelected ? Colors.white : Color(0xff666666),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      },
    );
  }
}
