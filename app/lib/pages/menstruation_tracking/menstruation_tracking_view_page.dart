import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:auto_route/auto_route.dart';

import 'package:account_management/application/menstruation_tracking_watcher/menstruation_tracking_watcher_bloc.dart';
import 'package:account_management/application/menstruation_tracking_manager/menstruation_tracking_manager_bloc.dart';
import 'package:account_management/di/di.dart';

import 'widgets/custom_menstruation_calendar.dart';
import 'widgets/menstruation_calendar_legend.dart';
import 'widgets/edit_mode_toolbar.dart';
import 'widgets/cycle_insights_card.dart';

@RoutePage()
class MenstruationTrackingViewPage extends StatelessWidget {
  const MenstruationTrackingViewPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<MenstruationTrackingWatcherBloc>(
          create: (context) => getIt<MenstruationTrackingWatcherBloc>()
            ..add(const MenstruationTrackingWatcherEvent.watchStarted()),
        ),
        BlocProvider<MenstruationTrackingManagerBloc>(
          create: (context) => getIt<MenstruationTrackingManagerBloc>(),
        ),
      ],
      child: const MenstruationTrackingViewScaffold(),
    );
  }
}

class MenstruationTrackingViewScaffold extends StatefulWidget {
  const MenstruationTrackingViewScaffold({Key? key}) : super(key: key);

  @override
  State<MenstruationTrackingViewScaffold> createState() =>
      _MenstruationTrackingViewScaffoldState();
}

class _MenstruationTrackingViewScaffoldState
    extends State<MenstruationTrackingViewScaffold> {
  DateTime _focusedMonth = DateTime.now();

  bool _isEditMode(MenstruationTrackingManagerState managerState) {
    return managerState.maybeMap(
      editModeChanged: (EditModeChanged state) => state.isEditMode,
      saveSuccess: (SaveSuccess state) => state.isEditMode,
      orElse: () => false,
    );
  }

  Set<DateTime> _getPendingSelectedDates(
      MenstruationTrackingManagerState managerState) {
    return managerState.maybeMap(
      editModeChanged: (EditModeChanged state) => state.pendingSelectedDates,
      saveSuccess: (SaveSuccess state) => state.pendingSelectedDates,
      orElse: () => <DateTime>{},
    );
  }

  Set<DateTime> _getPendingRemovedDates(
      MenstruationTrackingManagerState managerState) {
    return managerState.maybeMap(
      editModeChanged: (EditModeChanged state) => state.pendingRemovedDates,
      saveSuccess: (SaveSuccess state) => state.pendingRemovedDates,
      orElse: () => <DateTime>{},
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffFAF2DF),
      appBar: AppBar(
        backgroundColor: Color(0xff30285D),
        elevation: 0,
        leading: IconButton(
          onPressed: () => context.router.pop(),
          icon: Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24.sp,
          ),
        ),
        title: Text(
          'Menstruation Tracking',
          style: GoogleFonts.roboto(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        actions: [
          BlocBuilder<MenstruationTrackingWatcherBloc,
              MenstruationTrackingWatcherState>(
            builder: (context, state) {
              return state.maybeMap(
                loaded: (loadedState) => IconButton(
                  onPressed: () {
                    context.read<MenstruationTrackingWatcherBloc>().add(
                          const MenstruationTrackingWatcherEvent.watchStarted(),
                        );
                  },
                  icon: Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 24.sp,
                  ),
                ),
                orElse: () => SizedBox.shrink(),
              );
            },
          ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<MenstruationTrackingWatcherBloc,
              MenstruationTrackingWatcherState>(
            listener: (context, state) {
              state.maybeMap(
                error: (errorState) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Failed to load data',
                        style: GoogleFonts.roboto(color: Colors.white),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                orElse: () {},
              );
            },
          ),
          BlocListener<MenstruationTrackingManagerBloc,
              MenstruationTrackingManagerState>(
            listener: (context, state) {
              state.maybeMap(
                error: (errorState) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        errorState.message,
                        style: GoogleFonts.roboto(color: Colors.white),
                      ),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
                saveSuccess: (_) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Changes saved successfully',
                        style: GoogleFonts.roboto(color: Colors.white),
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                orElse: () {},
              );
            },
          ),
        ],
        child: BlocBuilder<MenstruationTrackingWatcherBloc,
            MenstruationTrackingWatcherState>(
          builder: (context, watcherState) {
            return BlocBuilder<MenstruationTrackingManagerBloc,
                MenstruationTrackingManagerState>(
              builder: (context, managerState) {
                return watcherState.map(
                  initial: (_) => _buildLoadingState(),
                  loading: (_) => _buildLoadingState(),
                  loaded: (loadedState) =>
                      _buildLoadedState(context, loadedState, managerState),
                  error: (errorState) => _buildErrorState(context, errorState),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xff30285D)),
          ),
          SizedBox(height: 16.h),
          Text(
            'Loading your cycle data...',
            style: GoogleFonts.roboto(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Color(0xff6C618B),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadedState(
    BuildContext context,
    dynamic watcherState,
    MenstruationTrackingManagerState managerState,
  ) {
    return Column(
      children: [
        // Cycle insights card
        if (!_isEditMode(managerState))
          CycleInsightsCard(
            nextPeriodDate: _getNextPeriodDate((watcherState.futurePredictions
                    as Map<String, Set<DateTime>>?) ??
                {}),
            nextOvulationDate: _getNextOvulationDate((watcherState
                    .futurePredictions as Map<String, Set<DateTime>>?) ??
                {}),
            cycleLength: 28, // This should come from user data
            periodLength: 6, // This should come from user data
            currentCycleDay: _getCurrentCycleDay(
                (watcherState.selectedPeriodDates as Set<DateTime>?) ??
                    <DateTime>{}),
          ),

        // Calendar
        Expanded(
          child: Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8.r,
                  offset: Offset(0, 2.h),
                ),
              ],
            ),
            child: CustomMenstruationCalendar(
              focusedMonth: _focusedMonth,
              selectedPeriodDates:
                  (watcherState.selectedPeriodDates as Set<DateTime>?) ??
                      <DateTime>{},
              ovulationDates: (watcherState.ovulationDates as Set<DateTime>?) ??
                  <DateTime>{},
              pendingSelectedDates: _getPendingSelectedDates(managerState),
              pendingRemovedDates: _getPendingRemovedDates(managerState),
              isEditMode: _isEditMode(managerState),
              onDayTapped: (date) {
                if (_isEditMode(managerState)) {
                  context.read<MenstruationTrackingManagerBloc>().add(
                        MenstruationTrackingManagerEvent.periodDateToggled(
                            date),
                      );
                } else {
                  context.read<MenstruationTrackingManagerBloc>().add(
                        MenstruationTrackingManagerEvent.daySelected(date),
                      );
                }
              },
              onMonthChanged: (month) {
                setState(() {
                  _focusedMonth = month;
                });
                // No need to add event for month navigation in the new architecture
              },
            ),
          ),
        ),

        // Legend (only show when not in edit mode)
        if (!_isEditMode(managerState))
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: MenstruationCalendarLegend(),
          ),

        // Edit mode toolbar
        EditModeToolbar(
          isEditMode: _isEditMode(managerState),
          isSaving: managerState.maybeMap(
            saving: (_) => true,
            orElse: () => false,
          ),
          pendingChangesCount: _getPendingSelectedDates(managerState).length +
              _getPendingRemovedDates(managerState).length,
          onEditPressed: () {
            context.read<MenstruationTrackingManagerBloc>().add(
                  const MenstruationTrackingManagerEvent.editModeEntered(),
                );
          },
          onCancelPressed: () {
            context.read<MenstruationTrackingManagerBloc>().add(
                  const MenstruationTrackingManagerEvent.editModeCancelled(),
                );
          },
          onSavePressed: () {
            context.read<MenstruationTrackingManagerBloc>().add(
                  const MenstruationTrackingManagerEvent.periodDatesSaved(),
                );
          },
        ),

        SizedBox(height: 16.h),
      ],
    );
  }

  Widget _buildSavingState(
    BuildContext context,
    dynamic state,
  ) {
    return Column(
      children: [
        // Show saving indicator
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          color: Color(0xff4CAF50).withOpacity(0.1),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 16.w,
                height: 16.h,
                child: CircularProgressIndicator(
                  strokeWidth: 2.w,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xff4CAF50)),
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                'Saving changes...',
                style: GoogleFonts.roboto(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: Color(0xff4CAF50),
                ),
              ),
            ],
          ),
        ),
        // Rest of the UI (similar to loaded state but disabled)
        Expanded(
          child: Container(
            margin: EdgeInsets.all(16.w),
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: CustomMenstruationCalendar(
              focusedMonth: _focusedMonth,
              selectedPeriodDates:
                  (state.selectedPeriodDates as Set<DateTime>?) ?? <DateTime>{},
              ovulationDates:
                  (state.ovulationDates as Set<DateTime>?) ?? <DateTime>{},
              pendingSelectedDates:
                  (state.pendingSelectedDates as Set<DateTime>?) ??
                      <DateTime>{},
              pendingRemovedDates:
                  (state.pendingRemovedDates as Set<DateTime>?) ?? <DateTime>{},
              isEditMode: false, // Disable interaction while saving
              onDayTapped: (_) {}, // Disabled
              onMonthChanged: (_) {}, // Disabled
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSyncingState(
    BuildContext context,
    dynamic state,
  ) {
    return _buildSavingState(context, state);
  }

  Widget _buildErrorState(
    BuildContext context,
    dynamic state,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: Colors.red,
          ),
          SizedBox(height: 16.h),
          Text(
            'Something went wrong',
            style: GoogleFonts.roboto(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: Color(0xff333333),
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            (state.message as String?) ?? 'Please try again',
            style: GoogleFonts.roboto(
              fontSize: 14.sp,
              fontWeight: FontWeight.w400,
              color: Color(0xff666666),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: () {
              context.read<MenstruationTrackingWatcherBloc>().add(
                    const MenstruationTrackingWatcherEvent.watchStarted(),
                  );
            },
            child: Text('Try Again'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xff30285D),
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  DateTime? _getNextPeriodDate(Map<String, Set<DateTime>> predictions) {
    final periods = predictions['periods'];
    if (periods == null || periods.isEmpty) return null;

    final now = DateTime.now();
    final futurePeriods = periods.where((date) => date.isAfter(now)).toList();
    futurePeriods.sort();

    return futurePeriods.isNotEmpty ? futurePeriods.first : null;
  }

  DateTime? _getNextOvulationDate(Map<String, Set<DateTime>> predictions) {
    final ovulations = predictions['ovulations'];
    if (ovulations == null || ovulations.isEmpty) return null;

    final now = DateTime.now();
    final futureOvulations =
        ovulations.where((date) => date.isAfter(now)).toList();
    futureOvulations.sort();

    return futureOvulations.isNotEmpty ? futureOvulations.first : null;
  }

  int _getCurrentCycleDay(Set<DateTime> periodDates) {
    if (periodDates.isEmpty) return 1;

    final now = DateTime.now();
    final sortedDates = periodDates.toList()..sort();

    // Find the most recent period start date
    DateTime? lastPeriodStart;
    for (final date in sortedDates.reversed) {
      if (date.isBefore(now) || date.isAtSameMomentAs(now)) {
        lastPeriodStart = date;
        break;
      }
    }

    if (lastPeriodStart == null) return 1;

    return now.difference(lastPeriodStart).inDays + 1;
  }
}
