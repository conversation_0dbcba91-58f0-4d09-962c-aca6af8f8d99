import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:auto_route/auto_route.dart';
import '../../../routing/app_pages.gr.dart';
import '../../../utils/url_utils.dart';
import 'settings_section.dart';
import 'common_settings_tile.dart';

class HelpCenterSection extends StatelessWidget {
  const HelpCenterSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SettingsSection(
      title: 'Help Center',
      children: [
        // Contact Us
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.headset,
            size: 24,
            color: const Color(0xff6750A4),
          ),
          title: 'Contact Us',
          onTap: () async {
            final success = await UrlUtils.launchContactUs();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Unable to open contact page. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // FAQ
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.circleQuestion,
            size: 24,
            color: const Color(0xff6750A4),
          ),
          title: 'FAQ',
          onTap: () {
            context.router.push(HelpCenterHomeRoute());
          },
        ),

        // Videos
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.play,
            size: 24,
            color: const Color(0xff6750A4),
          ),
          title: 'Videos',
          onTap: () {
            context.router.push(HelpCenterHomeRoute());
          },
        ),

        // Request a New Feature
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.lightbulb,
            size: 24,
            color: const Color(0xff6750A4),
          ),
          title: 'Request a New Feature',
          onTap: () async {
            final success = await UrlUtils.launchContactUs();
            if (!success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Unable to open contact page. Please try again.'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),

        // What's New
        SettingsTileStyles.defaultTile(
          icon: FaIcon(
            FontAwesomeIcons.star,
            size: 24,
            color: const Color(0xff6750A4),
          ),
          title: 'What\'s New',
          showDivider: false,
          onTap: () {
            // TODO: Implement What's New functionality
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('What\'s New feature coming soon!'),
                backgroundColor: Color(0xff6750A4),
              ),
            );
          },
        ),
      ],
    );
  }
}
