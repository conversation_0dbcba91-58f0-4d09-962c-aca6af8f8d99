import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class SettingsItem extends StatelessWidget {
  final FaIcon icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final Widget? trailing;
  final bool showDivider;

  const SettingsItem({
    Key? key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.trailing,
    this.showDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 10),
        GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.only(left: 10, right: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 30,
                  height: 30,
                  child: icon,
                ),
                const SizedBox(width: 25),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontSize: 18,
                              color: const Color(0xff26204a),
                              fontWeight: FontWeight.w400,
                            ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          subtitle!,
                          style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                fontSize: 14,
                                color: const Color(0xff26204a),
                                fontWeight: FontWeight.w300,
                              ),
                        ),
                      ],
                    ],
                  ),
                ),
                trailing ??
                    const Icon(
                      Icons.arrow_forward_ios,
                      size: 20,
                      color: Color(0xff6750A4),
                    ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (showDivider) const Padding(
          padding: EdgeInsets.symmetric(horizontal: 40),
          child: Divider(thickness: 1,height: 7,),
        ),
      ],
    );
  }
}


// class FaSettingsItem extends SettingsItem {
//    FaSettingsItem({
//     Key? key,
//     required IconData faIcon,
//     required String title,
//     String? subtitle,
//     VoidCallback? onTap,
//     Widget? trailing,
//     bool showDivider = true,
//     Color iconColor = const Color(0xff6750A4),
//   }) : super(
//           key: key,
//           icon: FaIcon(
//             faIcon,
//             size: 24,
//             color: iconColor,
//           ),
//           title: title,
//           subtitle: subtitle,
//           onTap: onTap,
//           trailing: trailing,
//           showDivider: showDivider,
//         );
// }
