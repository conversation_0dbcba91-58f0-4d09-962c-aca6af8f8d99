import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:account_management/application/account_watcher_bloc/account_watcher_bloc.dart';
import 'package:account_management/application/update_health_data_bloc/update_health_data_bloc.dart';
import '../../../helpers.dart';
import '../health_data_dialogs.dart';
import 'settings_section.dart';

class HealthDataSection extends StatelessWidget {
  const HealthDataSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountWatcherBloc, AccountWatcherState>(
      builder: (context, state) {
        return state.maybeWhen(
          loadSuccess: (accountDetails) {
            return SettingsSection(
              title: 'Health Data',
              children: [
                // Period Length
                _buildHealthDataItem(
                  context: context,
                  icon: 'assets/settings/period.svg',
                  title: 'Period Length',
                  subtitle:
                      '${accountDetails.healthData?.periodLength ?? 6} Days',
                  onTap: () {
                    showDialog<void>(
                      context: context,
                      builder: (context) => BlocProvider(
                        create: (context) => getIt<UpdateHealthDataBloc>(),
                        child: UpdatePeriodLengthDialog(
                          periodLength:
                              accountDetails.healthData?.periodLength ?? 6,
                        ),
                      ),
                    );
                  },
                ),

                // Cycle Length
                _buildHealthDataItem(
                  context: context,
                  icon: 'assets/settings/cycle.svg',
                  title: 'Cycle Length',
                  subtitle:
                      '${accountDetails.healthData?.cycleLength ?? 28} Days',
                  showDivider: false,
                  onTap: () {
                    showDialog<void>(
                      context: context,
                      builder: (context) => BlocProvider(
                        create: (context) => getIt<UpdateHealthDataBloc>(),
                        child: UpdateCycleLengthDialog(
                          cycleLength:
                              accountDetails.healthData?.cycleLength ?? 28,
                        ),
                      ),
                    );
                  },
                ),
              ],
            );
          },
          orElse: () => const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildHealthDataItem({
    required BuildContext context,
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.only(left: 10, right: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: 30,
                  height: 30,
                  child: SvgPicture.asset(
                    icon,
                    width: 30,
                    height: 30,
                  ),
                ),
                const SizedBox(width: 25),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                              fontSize: 18,
                              color: const Color(0xff26204a),
                              fontWeight: FontWeight.w400,
                            ),
                      ),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontSize: 16,
                              color: const Color(0xff26204a),
                              fontWeight: FontWeight.w100,
                            ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  size: 20,
                  color: Color(0xff6750A4),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (showDivider) const Divider(thickness: 1),
      ],
    );
  }
}
