import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'common_settings_tile.dart';

/// This file demonstrates how to use the CommonSettingsTile with different styles
/// You can copy these examples and modify them for your specific sections

class SettingsTileExamples extends StatelessWidget {
  const SettingsTileExamples({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings Tile Examples')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section 1: Default Style (Current design)
            _buildSectionHeader('Default Style'),
            SettingsTileStyles.defaultTile(
              icon: const FaIcon(FontAwesomeIcons.instagram, size: 24, color: Color(0xff6750A4)),
              title: 'Instagram',
              subtitle: 'Follow us on Instagram',
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 2: Compact Style (For dense sections)
            _buildSectionHeader('Compact Style'),
            SettingsTileStyles.compactTile(
              icon: const Icon(Icons.notifications, color: Color(0xff6750A4)),
              title: 'Period Reminder',
              subtitle: '3 days before',
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 3: Card Style (With background and shadow)
            _buildSectionHeader('Card Style'),
            SettingsTileStyles.cardTile(
              icon: const Icon(Icons.favorite, color: Color(0xff6750A4)),
              title: 'Health Data',
              subtitle: 'Manage your health information',
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 4: Prominent Style (For important actions)
            _buildSectionHeader('Prominent Style'),
            SettingsTileStyles.prominentTile(
              icon: const FaIcon(FontAwesomeIcons.cartShopping, size: 24, color: Color(0xff6750A4)),
              title: 'Get a Device',
              subtitle: 'Purchase your Juno device',
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 5: Danger Style (For destructive actions)
            _buildSectionHeader('Danger Style'),
            SettingsTileStyles.dangerTile(
              icon: const Icon(Icons.delete_forever, color: Colors.red),
              title: 'Delete Account & Data',
              subtitle: 'This action cannot be undone',
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 6: Custom Style (Fully customized)
            _buildSectionHeader('Custom Style'),
            CommonSettingsTile(
              icon: const FaIcon(FontAwesomeIcons.star, size: 28, color: Colors.amber),
              title: 'Premium Feature',
              subtitle: 'Unlock advanced features',
              backgroundColor: Colors.amber.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              titleColor: Colors.amber.shade700,
              titleFontWeight: FontWeight.bold,
              titleFontSize: 20,
              subtitleColor: Colors.amber.shade600,
              padding: const EdgeInsets.all(20),
              trailing: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.amber,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Text(
                  'NEW',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              onTap: () {},
            ),
            
            const SizedBox(height: 20),
            
            // Section 7: Switch Style (With toggle)
            _buildSectionHeader('With Switch'),
            SettingsTileStyles.defaultTile(
              icon: const Icon(Icons.dark_mode, color: Color(0xff6750A4)),
              title: 'Dark Mode',
              subtitle: 'Enable dark theme',
              trailing: Switch(
                value: true,
                onChanged: (value) {},
                activeColor: const Color(0xff6750A4),
              ),
              onTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Color(0xff26204a),
        ),
      ),
    );
  }
}

/// Quick reference for using different styles:
/// 
/// 1. DEFAULT STYLE (Current design):
/// SettingsTileStyles.defaultTile(...)
/// 
/// 2. COMPACT STYLE (Smaller, denser):
/// SettingsTileStyles.compactTile(...)
/// 
/// 3. CARD STYLE (With background):
/// SettingsTileStyles.cardTile(...)
/// 
/// 4. PROMINENT STYLE (For important items):
/// SettingsTileStyles.prominentTile(...)
/// 
/// 5. DANGER STYLE (For destructive actions):
/// SettingsTileStyles.dangerTile(...)
/// 
/// 6. FULLY CUSTOM:
/// CommonSettingsTile(
///   // All parameters are customizable
///   backgroundColor: Colors.blue,
///   borderRadius: BorderRadius.circular(20),
///   titleColor: Colors.white,
///   // ... etc
/// )
/// 
/// Easy to change designs:
/// - Just change the style method call
/// - Or add custom parameters to CommonSettingsTile
/// - Create new predefined styles in SettingsTileStyles class
