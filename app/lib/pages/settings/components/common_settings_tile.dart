import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonSettingsTile extends StatelessWidget {
  final Widget icon;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final Widget? trailing;
  final bool showDivider;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final double? iconSize;
  final Color? titleColor;
  final Color? subtitleColor;
  final double? titleFontSize;
  final double? subtitleFontSize;
  final FontWeight? titleFontWeight;
  final FontWeight? subtitleFontWeight;
  final double? spacing;
  final MainAxisAlignment? mainAxisAlignment;
  final CrossAxisAlignment? crossAxisAlignment;

  const CommonSettingsTile({
    Key? key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.onTap,
    this.trailing,
    this.showDivider = true,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
    this.boxShadow,
    this.iconSize,
    this.titleColor,
    this.subtitleColor,
    this.titleFontSize,
    this.subtitleFontSize,
    this.titleFontWeight,
    this.subtitleFontWeight,
    this.spacing,
    this.mainAxisAlignment,
    this.crossAxisAlignment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: borderRadius,
            boxShadow: boxShadow,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: borderRadius,
              child: Padding(
                padding: padding ?? const EdgeInsets.only(left: 10, right: 4),
                child: Row(
                  mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
                  crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: iconSize ?? 30,
                      height: iconSize ?? 30,
                      child: icon,
                    ),
                    SizedBox(width: spacing ?? 25),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  fontSize: titleFontSize ?? 18,
                                  color: titleColor ?? const Color(0xff26204a),
                                  fontWeight: titleFontWeight ?? FontWeight.w400,
                                ),
                          ),
                          if (subtitle != null) ...[
                            const SizedBox(height: 2),
                            Text(
                              subtitle!,
                              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                    fontSize: subtitleFontSize ?? 16,
                                    color: subtitleColor ?? const Color(0xff26204a),
                                    fontWeight: subtitleFontWeight ?? FontWeight.w100,
                                  ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (trailing != null) ...[
                      const SizedBox(width: 10),
                      trailing!,
                    ] else ...[
                      const Icon(
                        Icons.arrow_forward_ios,
                        size: 20,
                        color: Color(0xff6750A4),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (showDivider) const Divider(thickness: 1),
      ],
    );
  }
}

// Predefined styles for different sections
class SettingsTileStyles {
  // Default style (current design)
  static CommonSettingsTile defaultTile({
    required Widget icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showDivider = true,
  }) {
    return CommonSettingsTile(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing,
      showDivider: showDivider,
    );
  }

  // Compact style for dense sections
  static CommonSettingsTile compactTile({
    required Widget icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showDivider = true,
  }) {
    return CommonSettingsTile(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing,
      showDivider: showDivider,
      padding: const EdgeInsets.only(left: 8, right: 4),
      iconSize: 24,
      titleFontSize: 16,
      subtitleFontSize: 14,
      spacing: 20,
    );
  }

  // Card style with background and shadow
  static CommonSettingsTile cardTile({
    required Widget icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showDivider = false,
  }) {
    return CommonSettingsTile(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing,
      showDivider: showDivider,
      backgroundColor: const Color(0xffFAF2DF),
      borderRadius: BorderRadius.circular(12),
      boxShadow: const [
        BoxShadow(
          color: Color(0x40000000),
          blurRadius: 4.0,
          offset: Offset(0, 1),
        ),
      ],
      padding: const EdgeInsets.all(16),
    );
  }

  // Prominent style for important actions
  static CommonSettingsTile prominentTile({
    required Widget icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showDivider = true,
    Color? accentColor,
  }) {
    final color = accentColor ?? const Color(0xff6750A4);
    return CommonSettingsTile(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing,
      showDivider: showDivider,
      titleColor: color,
      titleFontWeight: FontWeight.w600,
      titleFontSize: 19,
    );
  }

  // Danger style for destructive actions
  static CommonSettingsTile dangerTile({
    required Widget icon,
    required String title,
    String? subtitle,
    VoidCallback? onTap,
    Widget? trailing,
    bool showDivider = true,
  }) {
    return CommonSettingsTile(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      trailing: trailing,
      showDivider: showDivider,
      titleColor: Colors.red,
      subtitleColor: Colors.red.withOpacity(0.7),
      titleFontWeight: FontWeight.w500,
    );
  }
}
