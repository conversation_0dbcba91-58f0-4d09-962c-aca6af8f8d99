# Juno Firmware - OTA Updates & Cybersecurity

## Overview

This document outlines the Over-The-Air (OTA) update mechanism and cybersecurity measures implemented in the Juno medical device firmware. The system is designed to meet medical device cybersecurity standards while enabling secure field updates.

## 🔒 CRITICAL SECURITY UPDATE - APPLICATION PROTECTION ENABLED

**IMPORTANT**: Application protection is now enabled for cybersecurity compliance. This introduces the following requirements:

### Security Requirements:
1. **Full Chip Erase Required**: A complete chip erase must be performed before flashing new firmware to clear protection flags
2. **Hardware Compatibility**: Configuration changes have been made for hardware differences between DevKit and Juno board
3. **Battery Requirements**: Device must have >50% battery charge for secure updates
4. **Secure Boot Process**: MC<PERSON>boot automatically handles the secure update process with APPROTECT enabled

### Implementation Status: ✅ COMPLETE
- ✅ OTA system integrated with cybersecurity compliance
- ✅ Application protection handling implemented
- ✅ Full chip erase process integrated
- ✅ Hardware compatibility checks added
- ✅ Security warnings added to user interface

---

## 🔄 Over-The-Air (OTA) Updates

### Architecture

The Juno firmware implements a secure OTA update system using **MCUboot** bootloader with **Bluetooth Low Energy (BLE)** transport.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │───▶│  BLE Transport  │───▶│  Juno Device    │
│                 │    │                 │    │                 │
│ - Update Images │    │ - Encrypted     │    │ - MCUboot       │
│ - Signature     │    │ - Chunked       │    │ - Verification  │
│ - Verification  │    │                 │    │ - Installation  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Components

#### 1. **MCUboot Bootloader**
- **Secure Boot**: Verifies firmware signatures on every boot
- **Dual-Bank Updates**: Safely installs new firmware in secondary slot
- **Image Validation**: Cryptographic verification using ECDSA P-256

#### 2. **Bluetooth DFU Service**
- **BLE Transport**: OTA data transmitted via Bluetooth Low Energy
- **Progress Monitoring**: Real-time update status and error reporting
- **Fragmented Transfer**: Large firmware images sent in manageable chunks

#### 3. **Flash Memory Layout**
```
Flash Partitions (nRF5340):
┌──────────────────┐ 0x00000000
│    MCUboot       │ (64KB)
├──────────────────┤ 0x00010000
│    Image-0       │ (256KB) - Current Firmware
│    (Secure)      │
├──────────────────┤ 0x00050000
│  Image-0-NS      │ (192KB) - Current Firmware
│  (Non-Secure)    │          (Non-Secure part)
├──────────────────┤ 0x00080000
│    Image-1       │ (256KB) - Update Slot
│    (Secure)      │
├──────────────────┤ 0x000C0000
│  Image-1-NS      │ (192KB) - Update Slot
│  (Non-Secure)    │          (Non-Secure part)
├──────────────────┤ 0x000F0000
│   TF-M Storage   │ (32KB)  - Secure Storage
├──────────────────┤ 0x000F8000
│   User Storage   │ (32KB)  - Settings/Data
└──────────────────┘ 0x00100000
```

### Update Process

1. **Pre-Update Verification**
   - Firmware version validation
   - Available space verification

2. **Image Transfer**
   - Chunk-by-chunk transfer via BLE
   - Real-time integrity verification
   - Resume capability for interrupted transfers
   - Progress indication to user

3. **Installation & Verification**
   - Image signature verification using stored public key
   - Hash validation of complete image
   - Metadata validation (version, compatibility)
   - Safe installation to secondary slot

4. **Activation**
   - Device reboot initiated
   - MCUboot validates new image
   - If valid: Switch to new firmware
   - If invalid: Fallback to previous version

---

## 🛡️ Cybersecurity Measures

### Hardware-Level Security

#### **Access Port Protection (APPROTECT)**
```c
// Production devices have debug ports permanently locked
CONFIG_NRF_APPROTECT_LOCK=y              // Standard debug protection
CONFIG_NRF_SECURE_APPROTECT_LOCK=y       // Secure debug protection
CONFIG_NRF_APPROTECT_USE_UICR=y          // Hardware register protection

// Runtime provisioning in release builds
provision_approtect() {
    // Writes 0x00 to UICR.APPROTECT registers
    // Irreversible after power cycle
}
```

**Benefits:**
- 🚫 Permanently disables JTAG/SWD debug access
- 🔒 Prevents firmware extraction or modification
- 🛡️ Protects against hardware-based attacks
- ⚡ One-time programming for production security

**⚠️ Important Note:**
- Once APPROTECT is enabled, you will need a **full flash erase** to reprogram the device
- This permanently removes all firmware and data from the device
- Use only for production devices, not during development

### Firmware Security

#### **Secure Boot Chain**
```
Power-On → MCUboot → Signature Verification → Application Launch
    ↓         ↓              ↓                      ↓
  Reset    SecureBoot    ECDSA P-256            Main App
  Vector   Bootloader   Public Key             Validated
                        Verification           Firmware
```

**Features:**
- **ECDSA P-256 Signatures**: Industry-standard cryptographic verification
- **Public Key Pinning**: Embedded trusted keys prevent key substitution
- **Chain of Trust**: Each boot stage validates the next
- **Fail-Safe Mode**: System halts on verification failure

#### **Runtime Security**
```c
// Stack protection against overflow attacks
CONFIG_HW_STACK_PROTECTION=y

// Watchdog protection (development only)
CONFIG_WATCHDOG=n              // Currently disabled in release
```

### Communication Security

#### **Bluetooth Low Energy (BLE) Security**

**Basic BLE Configuration:**
- **Peripheral Mode**: Device acts as BLE peripheral
- **SMP Support**: Security Manager Protocol enabled
- **Device Information**: Basic device identification

**Security Features:**
```c
CONFIG_BT_SMP=y                    // Security Manager Protocol
CONFIG_BT_PERIPHERAL=y             // Peripheral-only mode
CONFIG_BT_DIS_SETTINGS=y           // Device information service
```

**GATT Characteristic Access:**
```
┌─────────────────────┬──────────────────┬─────────────────┐
│    Characteristic   │   Requirements   │   Protection    │
├─────────────────────┼──────────────────┼─────────────────┤
│ Device Control      │ Read/Write       │ Basic BLE       │
│ Heat Control        │ Access           │ Security        │
│ TENS Control        │                  │                 │
│ Status Monitoring   │                  │                 │
└─────────────────────┴──────────────────┴─────────────────┘
```

### Data Protection

#### **Secure Storage**
- **Settings Encryption**: Device configuration stored securely
- **Key Management**: Encryption keys in secure storage regions
- **Integrity Protection**: Checksums prevent tampering
- **Wear Leveling**: Even flash usage prevents data recovery

#### **Fault Handling & Logging**
```c
// Comprehensive fault detection and response
fault_handler() {
    // Log fault details to secure storage
    // Attempt recovery or transition to safe state
    // Prevent exploitation of fault conditions
}
```

---

## 🔧 Configuration

### Development vs Production

#### **Development Build (`prj.conf`)**
```ini
# Debug & logging enabled
CONFIG_LOG=y
CONFIG_DEBUG_THREAD_INFO=y
CONFIG_USE_SEGGER_RTT=y

# Development device name
CONFIG_BT_DEVICE_NAME="frida"
CONFIG_BT_DIS_SERIAL_NUMBER_STR="Zephyr Serial"

# APPROTECT disabled for debugging
# (No approtect provisioning code runs)
```

#### **Production Build (`prj_release.conf`)**
```ini
# All debug/logging disabled
CONFIG_LOG=n
CONFIG_USE_SEGGER_RTT=n
CONFIG_CONSOLE=n

# Production device identity
CONFIG_BT_DEVICE_NAME="Juno"
CONFIG_BT_DIS_SERIAL_NUMBER_STR="sn"

# Security features enabled
CONFIG_NRF_APPROTECT_LOCK=y
CONFIG_NRFX_NVMC=y              # For UICR writing
```

### MCUboot Configuration (`sysbuild.conf`)
```ini
# Secure bootloader with signature verification
SB_CONFIG_BOOTLOADER_MCUBOOT=y
SB_CONFIG_MCUBOOT_MODE_OVERWRITE_ONLY=y

# ECDSA P-256 signature verification
SB_CONFIG_BOOT_SIGNATURE_TYPE_ECDSA_P256=y
SB_CONFIG_BOOT_SIGNATURE_KEY_FILE="${APP_DIR}/private_key.pem"

# Flash layout management
SB_CONFIG_PARTITION_MANAGER=y
```


## 🔄 Update Procedures

### For Developers

1. **Generate Key Pair** (one-time):
   ```bash
   # Generate ECDSA P-256 private key using imgtool
   imgtool.py keygen -k private_key.pem -t ecdsa-p256
   
   # Extract public key for embedding
   imgtool.py getpub -k private_key.pem > public_key.pem
   ```

2. **Build Signed Firmware**:
   ```bash
   # Development build
   west build -p -b juno_board_vy_nrf5340_cpuapp_ns
   
   # Release build  
   west build -p -b juno_board_vy_nrf5340_cpuapp_ns -- -DCONF_FILE=prj_release.conf
   ```

3. **Generate Update Package**:
   ```bash
   # MCUboot creates the DFU application package automatically
   # Located in: build/zephyr/dfu_application.zip
   ```

### For Field Updates

1. **Prepare Device**:
   - Establish BLE connection
   - Ensure sufficient storage space

2. **Initiate Update**:
   - Select firmware file in mobile app
   - Start update transfer
   - Monitor progress indicators

3. **Verify Success**:
   - Device reboots automatically
   - New firmware version displayed
   - All functions tested



