
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>SBOM Report</title>
	<style>
		@import url('https://fonts.googleapis.com/css2?family=Lato&family=Roboto+Slab&display=swap');
		/* Nordic Default Theme */
		body {
			font-family: Lato,proxima-nova,Helvetica Neue,Arial,sans-serif;
			color: #404040;
			margin: 51px;
			font-size: 16px;
			background-color: white;
		}
		pre {
			font-family: SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,Courier,monospace;
			font-size: 12px;
			line-height: 1.4;
			padding: 12px;
			border: 1px solid #e1e4e5;
			margin: 1px 0 24px;
			background: #f8f8f8;
			min-width: fit-content;
		}
		pre.list {
			padding: 0px 12px 12px;
		}
		h1 {
			font-family: Roboto Slab,ff-tisa-web-pro,Georgia,Arial,sans-serif;
			border-top: 1px solid #ddd;
			padding-top: 20px;
			margin-top: 0px;
			margin-bottom: 24px;
		}
		h2 {
			font-family: Roboto Slab,ff-tisa-web-pro,Georgia,Arial,sans-serif;
			border-top: 1px solid #ddd;
			padding-top: 24px;
			margin-top: 0px;
			margin-bottom: 24px;
		}
		a {
			color: #00a9ce;
			text-decoration: none;
		}
		a:hover {
			color: #0077C8;
		}
		div, p {
			line-height: 24px;
			margin-block-start: 1em;
			margin-block-end: 1em;
			margin-inline-start: 0px;
			margin-inline-end: 0px;
		}
		.src {
			font-family: SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,Courier,monospace;
			font-size: 12px;
		}
		.fa {
			text-decoration: none;
			color: #404040;
		}
		.fa:hover {
			text-decoration: none;
			color: #0077C8;
		}
		div.package {
			background: #e7f2fa;
			color: #404040;
			padding: 8px 12px;
			margin: 16px 0px 8px;
			line-height: 1.7;
			border: 1px solid #e1e4e5;
		}
		span.modifications {
			font-weight: bold;
			display: inline-block;
			border: 1px solid #e1e4e5;
			padding: 3px 7px 2px;
			border-radius: 4px;
			background-color: white;
		}
		div.note-header {
			background: #6ab0de;
			color: white;
			font-weight: bold;
			padding: 4px 4px 4px 12px;
			margin-bottom: 0px;
		}
		div.note-content {
			background: #e7f2fa;
			color: #404040;
			padding: 12px;
			margin-top: 0px;
		}
		a#scroll-up {
			line-height: normal;
			position: fixed;
			bottom: 20px;
			right: 20px;
			display: block;
			background: #6ab0de;
			width: 56px;
			height: 50px;
			border-radius: 28px;
			color: white;
			text-align: center;
			font-size: 30px;
			padding-top: 6px;
			box-shadow: 1px 3px 10px 0px #222222
		}
		a#scroll-up:hover {
			box-shadow: 1px 3px 14px 3px #222222
		}
		span[class^="-icon-"] {
			width: 14px;
			height: 14px;
			vertical-align: -1px;
			display: inline-block;
			background-size: 100% 100%;
		}
		span.-icon-exclamation {
			background-image: url('data:image/svg+xml;charset=utf-8,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22UTF-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20viewBox%3D%220%200%2016%2016%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Ccircle%20cx%3D%228%22%20cy%3D%228%22%20r%3D%228%22%20fill%3D%22%23fff%22%2F%3E%3Crect%20x%3D%226.5%22%20y%3D%222.5%22%20width%3D%223%22%20height%3D%227%22%20fill%3D%22%236ab0de%22%2F%3E%3Crect%20x%3D%226.5%22%20y%3D%2210.5%22%20width%3D%223%22%20height%3D%223%22%20fill%3D%22%236ab0de%22%2F%3E%3C%2Fsvg%3E');
		}
		/* Dark Color Theme */
		@media (prefers-color-scheme: dark) {
			body {
				color: #cccccc;
				background-color: #292929;
			}
			pre {
				border: 1px solid #474747;
				background: #333333;
			}
			h2, h1 {
				border-top-color: #444444;
			}
			a:hover {
				color: #66d1e9;
			}
			.fa {
				color: #cccccc;
			}
			.fa:hover {
				color: #66d1e9;
			}
			div.package {
				background: #2c3e49;
				color: #cccccc;
				border: 1px solid #474747;
			}
			span.modifications {
				border: 1px solid #474747;
				background-color: #292929;
			}
			div.note-header, a#scroll-up {
				background: #447291;
			}
			div.note-content {
				background: #2c3e49;
				color: #cccccc;
			}
		}
		/* Visual Studio Code Color Theme */
		html[style*="--vscode"] body {
			color: var(--vscode-editor-foreground);
			background-color: var(--vscode-editor-background);
		}
		html[style*="--vscode"] pre {
			border: 1px solid var(--vscode-panel-border);
			background: var(--vscode-sideBar-background);
		}
		html[style*="--vscode"] h2, html[style*="--vscode"] h1 {
			border-top-color: var(--vscode-panel-border);
		}
		html[style*="--vscode"] a {
			color: var(--vscode-textLink-foreground);
			text-decoration: none;
		}
		html[style*="--vscode"] a:hover {
			text-decoration: underline;
			color: var(--vscode-textLink-activeForeground);
		}
		html[style*="--vscode"] .fa {
			color: var(--vscode-foreground);
		}
		html[style*="--vscode"] .fa:hover {
			text-decoration: none;
			color: var(--vscode-textLink-activeForeground);
		}
		html[style*="--vscode"] div.package {
			background: var(--vscode-quickInput-background);
			color: var(--vscode-quickInput-foreground);
			border: 1px solid var(--vscode-panel-border);
		}
		html[style*="--vscode"] span.modifications {
			border: 1px solid var(--vscode-panel-border);
			background-color: var(--vscode-quickInput-background);
		}
		html[style*="--vscode"] div.note-header, html[style*="--vscode"] a#scroll-up {
			text-decoration: none;
			background: var(--vscode-button-background);
			color: var(--vscode-button-foreground);
		}
		html[style*="--vscode"] div.note-content {
			background: var(--vscode-quickInput-background);
			color: var(--vscode-quickInput-foreground);
		}
	</style>
</head>

<body>
	<a id="scroll-up" href="#">▲</a>

	<h1>Software Bill of Material Report</h1>

	
	<div class="note-header">
		<span class="-icon-exclamation"></span>&nbsp; Note
	</div>
	<div class="note-content">
		Some of the files in the report do no contain license information or it cannot be detected.
		See <a href="#no-license">license details</a> for list of such files.
		You need to evaluate them manually to get the license information.
	</div>
	

	

	<div>This report was generated from the following inputs:</div>
	<ul>
		
		<li><span class="src">The &#34;zephyr/zephyr.elf&#34; file from the build directory &#34;/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw&#34;</span></li>
		
		<li><span class="src">The &#34;zephyr/zephyr.elf&#34; file from the build directory &#34;/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot&#34;</span></li>
		
		<li><span class="src">The &#34;zephyr/zephyr.elf&#34; file from the build directory &#34;/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio&#34;</span></li>
		
	</ul>

	<div>The files are covered by the following licenses:</div>
	<ul>
		
		
		
		
		<li><a href="#BSD-2-CLAUSE AND LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">BSD-2-Clause AND LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
		
		
		<li><a href="#LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
		
		
		<li><a href="#LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">LicenseRef-west-ncs-sbom-BSD-1-Clause-SEGGER</a></li>
		
		
		
		<li><a href="#LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
		
		
		<li><a href="#NORDIC-5-CLAUSE">NORDIC-5-CLAUSE</a></li>
		
		
		
		<li><a href="#APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</a></li>
		
		
		
		<li><a href="#GPL-3.0-OR-LATER WITH GCC-EXCEPTION-3.1">GPL-3.0-or-later WITH GCC-EXCEPTION-3.1</a></li>
		
		
		
		<li><a href="#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		<li><a href="#BSD-2-CLAUSE">BSD-2-Clause</a></li>
		
		
		
		<li><a href="#BSD-2-CLAUSE-FREEBSD">BSD-2-Clause-FreeBSD</a></li>
		
		
		
		<li><a href="#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		<li><a href="#MIT">MIT</a></li>
		
		
		
		<li><a href="#no-license">Unknown licenses</a> that cannot be detected automatically</li>
		
	</ul>

	<div>The files are obtained from the following sources:</div>
	<ul>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MBEDTLS#98603A8C91660BEAC00E0EE1D76198FB7C4ED29B">nrfconnect/sdk-mbedtls, version 98603a8c91660beac00e0ee1d76198fb7c4ed29b</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot, version 4594a8693738004af89929cad12d33cdc82fbe6c</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf, version a2386bfc84016fa571f997ac871b25bd67ca481a</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90">nrfconnect/sdk-nrfxlib, version 342901a77938defa0edfa045f9bd90350958ae90</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395">nrfconnect/sdk-oberon-psa-crypto, version b41e899e7302462eb952b0b6a7c6903e368fb395</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr, version 0bc3393fb112ec80ebeab48cd023d69b1e9db757</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6">SolucionesKenko/juno, version 1ecd7f8c80f068d5f13c466532abe48230701ee6</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/CMSIS#4B96CBB174678DCD3CA86E11E1F24BC5F8726DA0">zephyrproject-rtos/cmsis, version 4b96cbb174678dcd3ca86e11e1f24bc5f8726da0</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717">zephyrproject-rtos/hal_nordic, version 5c8d109371ebb740fbef1f440a3b59e488a36717</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/LIBMETAL#A6851BA6DBA8C9E87D00C42F171A822F7A29639B">zephyrproject-rtos/libmetal, version a6851ba6dba8c9e87d00c42f171a822f7a29639b</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719">zephyrproject-rtos/open-amp, version b735edbc739ad59156eb55bb8ce2583d74537719</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/SEGGER#B011C45B585E097D95D9CF93EDF4F2E01588D3CD">zephyrproject-rtos/segger, version b011c45b585e097d95d9cf93edf4f2e01588d3cd</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/TINYCRYPT#1012A3EBEE18C15EDE5EFC8332EE2FC37817670F">zephyrproject-rtos/tinycrypt, version 1012a3ebee18c15ede5efc8332ee2fc37817670f</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/ZCBOR#47F34DD7F5284E8750B5A715DEE7F77C6C5BDC3F">zephyrproject-rtos/zcbor, version 47f34dd7f5284e8750b5a715dee7f77c6c5bdc3f</a></li>
		
		
		
		<li><a href="#no-package">Unknown source</a> that cannot be detected automatically</li>
		
	</ul>

	<div>The texts of the following licenses are added to this report:</div>
	<ul>
		
			<li><a href="#text-LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
			<li><a href="#text-LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">LicenseRef-west-ncs-sbom-BSD-1-Clause-SEGGER</a></li>
		
			<li><a href="#text-LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
	</ul>

	<h1 id="license-details">License details</h1>

	
	<h2 id="no-license">No license information detected</h2>
	<div>Files without any license information or with license information that cannot be detected automatically.</div>
	<pre class="list">
<div class="package" id="package##">Repository: Unknown
Version:    Unknown</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/ncs_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/ncs_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/ncs_version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/ncs_version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/pm_config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/pm_config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/autoconf.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/autoconf.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/devicetree_generated.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/devicetree_generated.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/kobj-types-enum.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/kobj-types-enum.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/offsets.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/offsets.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscall_list.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscall_list.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/device.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/device.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/entropy.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/entropy.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/gpio.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/gpio.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/i2c.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/i2c.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/kernel.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/kernel.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/kobject.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/kobject.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/libc-hooks.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/libc-hooks.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/log_ctrl.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/log_ctrl.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/log_msg.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/log_msg.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/mbox.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/mbox.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/random.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/random.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/rtio.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/syscalls/rtio.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr/version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/include/generated/zephyr_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/isr_tables.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/isr_tables.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/linker.cmd" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/linker.cmd</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/lib/s_veneers.o" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/lib/s_veneers.o</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/bin/tfm_s.hex" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/bin/tfm_s.hex</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/app_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/app_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/ncs_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/ncs_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/ncs_version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/ncs_version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/pm_config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/pm_config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/app_version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/app_version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/autoconf.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/autoconf.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/devicetree_generated.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/devicetree_generated.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/kobj-types-enum.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/kobj-types-enum.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/offsets.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/offsets.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscall_list.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscall_list.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/adc.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/adc.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/dac.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/dac.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/device.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/device.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/entropy.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/entropy.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/flash.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/flash.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/gpio.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/gpio.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/hwinfo.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/hwinfo.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/i2c.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/i2c.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/kernel.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/kernel.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/kobject.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/kobject.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/led.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/led.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/libc-hooks.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/libc-hooks.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/log_ctrl.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/log_ctrl.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/log_msg.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/log_msg.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/mbox.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/mbox.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/pwm.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/pwm.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/random.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/random.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/rtio.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/rtio.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/sensor.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/sensor.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/spi.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/syscalls/spi.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr/version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/include/generated/zephyr_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/isr_tables.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/isr_tables.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/linker.cmd" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/linker.cmd</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/autogen-pubkey.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/autogen-pubkey.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/app_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/app_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/libc/minimal/strerror_table.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/libc/minimal/strerror_table.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/ncs_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/ncs_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/ncs_version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/ncs_version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/pm_config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/pm_config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/app_version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/app_version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/autoconf.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/autoconf.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/devicetree_generated.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/devicetree_generated.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/kobj-types-enum.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/kobj-types-enum.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/offsets.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/offsets.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscall_list.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscall_list.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/device.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/device.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/entropy.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/entropy.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/errno_private.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/errno_private.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/flash.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/flash.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/gpio.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/gpio.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/kernel.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/kernel.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/kobject.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/kobject.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/libc-hooks.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/libc-hooks.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/log_ctrl.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/log_ctrl.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/log_msg.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/log_msg.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/pwm.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/pwm.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/random.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/syscalls/random.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/version.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr/version.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr_commit.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/include/generated/zephyr_commit.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/isr_tables.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/isr_tables.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/linker.cmd" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/linker.cmd</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/libgcc.a" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp/libgcc.a</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard/libgcc.a" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main+fp/hard/libgcc.a</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/libc.a" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp/libc.a</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard/libc.a" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main+fp/hard/libc.a</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/_ansi.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/_ansi.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/assert.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/assert.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/ctype.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/ctype.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/errno.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/errno.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/ieeefp.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/ieeefp.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/inttypes.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/inttypes.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/limits.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/limits.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_default_types.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_default_types.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_endian.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_endian.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_time.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_time.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_types.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/_types.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/endian.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/endian.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/ieeefp.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/ieeefp.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/stdlib.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/stdlib.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/time.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/time.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/types.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/types.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/math.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/math.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/picolibc.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/picolibc.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdint.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdint.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdio.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdio.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdlib.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/stdlib.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/string.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/string.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/strings.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/strings.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_intsup.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_intsup.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_locale.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_locale.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_stdint.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_stdint.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_types.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_types.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/config.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/config.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/errno.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/errno.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/features.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/features.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/string.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/string.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/syslimits.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/syslimits.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/timespec.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/timespec.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/types.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/types.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/time.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/time.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90">nrfconnect/sdk-nrfxlib</a>
Version:    342901a77938defa0edfa045f9bd90350958ae90</div><a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/hard-float/liboberon_3.0.15.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/hard-float/liboberon_3.0.15.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/hard-float/liboberon_mbedtls_3.0.15.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33/hard-float/liboberon_mbedtls_3.0.15.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33+nodsp/soft-float/liboberon_3.0.15.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/lib/cortex-m33+nodsp/soft-float/liboberon_3.0.15.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/common/lib/nrf53/soft-float/libmpsl_fem_common.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/common/lib/nrf53/soft-float/libmpsl_fem_common.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf21540_gpio/lib/nrf53/soft-float/libmpsl_fem_nrf21540_gpio.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf21540_gpio/lib/nrf53/soft-float/libmpsl_fem_nrf21540_gpio.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf21540_gpio_spi/lib/nrf53/soft-float/libmpsl_fem_nrf21540_gpio_spi.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf21540_gpio_spi/lib/nrf53/soft-float/libmpsl_fem_nrf21540_gpio_spi.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf2220/lib/nrf53/soft-float/libmpsl_fem_nrf2220.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf2220/lib/nrf53/soft-float/libmpsl_fem_nrf2220.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf2240/lib/nrf53/soft-float/libmpsl_fem_nrf2240.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf2240/lib/nrf53/soft-float/libmpsl_fem_nrf2240.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf22xx/lib/nrf53/soft-float/libmpsl_fem_nrf22xx.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/nrf22xx/lib/nrf53/soft-float/libmpsl_fem_nrf22xx.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/simple_gpio/lib/nrf53/soft-float/libmpsl_fem_simple_gpio.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/simple_gpio/lib/nrf53/soft-float/libmpsl_fem_simple_gpio.a</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr</a>
Version:    0bc3393fb112ec80ebeab48cd023d69b1e9db757</div><a href="file:////opt/nordic/ncs/v2.8.0/zephyr/misc/empty_file.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/misc/empty_file.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6#">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6">SolucionesKenko/juno</a>
Version:    1ecd7f8c80f068d5f13c466532abe48230701ee6</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/dac_controller.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/dac_controller.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/dac_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/dac_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/heat_pwm_controller.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/heat_pwm_controller.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/heat_pwm_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/heat_pwm_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/tens_pwm_controller.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/tens_pwm_controller.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/tens_pwm_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/tens_pwm_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/app_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/app_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/app_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/app_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ble_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ble_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ble_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ble_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/common_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/common_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/heat_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/heat_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/heat_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/heat_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/led_state_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/led_state_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/led_state_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/led_state_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/pwr_mgmt_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/pwr_mgmt_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/pwr_mgmt_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/pwr_mgmt_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/sensor_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/sensor_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/sensor_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/sensor_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/tens_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/tens_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/tens_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/tens_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ui_module_event.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ui_module_event.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ui_module_event.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/events/ui_module_event.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/main.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/main.c</a> <span class="modifications">with local modifications</span>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_module.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_module.c</a> <span class="modifications">with local modifications</span>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_device_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_device_control.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_device_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_device_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_heat_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_heat_control.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_heat_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_heat_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_status.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_status.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_status.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_status.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_tens_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_tens_control.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_tens_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ble/ble_svc_tens_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/modules_common.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/modules_common.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/modules_common.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/modules_common.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/charger_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/charger_control.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/charger_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/charger_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/pwr_mgmt.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/pwr_mgmt.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/pwr_mgmt.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/power/pwr_mgmt.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/heat_sensor.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/heat_sensor.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/heat_sensor.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/heat_sensor.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/sensor_module.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/sensor_module.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/tens_sensor.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/tens_sensor.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/tens_sensor.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/tens_sensor.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/thermistor.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/thermistor.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/thermistor.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/sensors/thermistor.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/heat_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/heat_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/impedance_driver.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/impedance_driver.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/impedance_driver.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/impedance_driver.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/lead_off_detection.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/lead_off_detection.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/lead_off_detection.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/lead_off_detection.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/pid_controller.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/pid_controller.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/pid_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/pid_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/signal_processing.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/signal_processing.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/signal_processing.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/signal_processing.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/tens_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/tens_control.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/tens_control.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/tens_control.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/button.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/button.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/button.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/button.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/led_module.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/led_module.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/led_state_def.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/led_state_def.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/tlc5947.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/tlc5947.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/tlc5947.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/tlc5947.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/ui_module.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/ui_module.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/ui_module.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/ui/ui_module.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/fault_handler.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/fault_handler.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/fault_handler.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/fault_handler.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/storage.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/storage.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/storage.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/safety/storage.h</a>
</pre>
	

	
	
	
	
	<h2 id="BSD-2-CLAUSE AND LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">BSD-2-Clause AND LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</h2>
	<div>
		
			The license expression
			
			
				containing the following licenses:<ul>
				
					<li>
					BSD-2-Clause<br />
					
					
						BSD 2-Clause &#34;Simplified&#34; License
					
					
						<br /><a href="https://spdx.org/licenses/BSD-2-Clause.html">https://spdx.org/licenses/BSD-2-Clause.html</a>
					
					
					</li>
				
					<li>
					LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel<br />
					
						Non-standard SPDX License:
					
					
						Variant of BSD-3-Clause license with &#34;Intel Corporation&#34; as a copyright holder.
					
					
					
						<br /><a href="#text-LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">The license text</a> is added to this report.
					
					</li>
				
			
			</ul>
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#BSD-2-CLAUSE AND LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot</a>
Version:    4594a8693738004af89929cad12d33cdc82fbe6c</div><a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc_dsa.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc_dsa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc_platform_specific.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/ecc_platform_specific.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/ecc.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/ecc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/ecc_dsa.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/ecc_dsa.c</a>
</pre>
	
	
	
	<h2 id="LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</h2>
	<div>
		
			
				Non-standard SPDX License:
			
			
				Nordic 5-Clause License
			
			
			
				<br /><a href="#text-LICENSEREF-NORDIC-5-CLAUSE">The license text</a> is added to this report.
			
		
	</div>
	<pre class="list">
<div class="package" id="package##LICENSEREF-NORDIC-5-CLAUSE">Repository: Unknown
Version:    Unknown</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/nrf-config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/nrf-config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/nrf-psa-crypto-config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/nrf-psa-crypto-config.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#LICENSEREF-NORDIC-5-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot</a>
Version:    4594a8693738004af89929cad12d33cdc82fbe6c</div><a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/nrf_cleanup.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/nrf_cleanup.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/sysflash/pm_sysflash.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/sysflash/pm_sysflash.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/nrf_cleanup.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/nrf_cleanup.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#LICENSEREF-NORDIC-5-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf</a>
Version:    a2386bfc84016fa571f997ac871b25bd67ca481a</div><a href="file:////opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/bt_hci_ipc.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/bt_hci_ipc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/ipc_bt.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/ipc_bt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/main.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/applications/ipc_radio/src/main.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/drivers/hw_cc3xx/hw_cc3xx.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/drivers/hw_cc3xx/hw_cc3xx.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/drivers/mpsl/clock_control/nrfx_clock_mpsl.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/drivers/mpsl/clock_control/nrfx_clock_mpsl.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/drivers/mpsl/clock_control/nrfx_power_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/drivers/mpsl/clock_control/nrfx_power_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/app_event_manager.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/app_event_manager.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/app_event_manager_profiler_tracer.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/app_event_manager_profiler_tracer.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/bluetooth/hci_vs_sdc.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/bluetooth/hci_vs_sdc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/bluetooth/nrf/host_extensions.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/bluetooth/nrf/host_extensions.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/caf/events/led_event.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/caf/events/led_event.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/caf/events/module_state_event.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/caf/events/module_state_event.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/caf/events/power_event.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/caf/events/power_event.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/caf/led_effect.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/caf/led_effect.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/dk_buttons_and_leds.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/dk_buttons_and_leds.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/flash_map_pm.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/flash_map_pm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/fprotect.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/fprotect.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/hw_id.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/hw_id.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_assert.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_assert.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_lib.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_lib.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_work.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/mpsl/mpsl_work.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/nrf_profiler.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/nrf_profiler.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/sfloat.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/sfloat.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/tfm/tfm_builtin_key_ids.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/tfm/tfm_builtin_key_ids.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/include/tfm/tfm_ioctl_api.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/include/tfm/tfm_ioctl_api.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/boot_banner/banner.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/boot_banner/banner.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/dk_buttons_and_leds/dk_buttons_and_leds.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/dk_buttons_and_leds/dk_buttons_and_leds.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/fprotect/fprotect_spu.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/fprotect/fprotect_spu.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/hw_id/hw_id.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/hw_id/hw_id.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/multithreading_lock/multithreading_lock.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/multithreading_lock/multithreading_lock.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/lib/multithreading_lock/multithreading_lock.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/lib/multithreading_lock/multithreading_lock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/modules/trusted-firmware-m/tfm_boards/src/tfm_ioctl_ns_api.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/modules/trusted-firmware-m/tfm_boards/src/tfm_ioctl_ns_api.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager/app_event_manager.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager/app_event_manager.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager/app_event_manager_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager/app_event_manager_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager_profiler_tracer/app_event_manager_profiler_tracer_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/app_event_manager_profiler_tracer/app_event_manager_profiler_tracer_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/bt_ctlr_used_resources.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/bt_ctlr_used_resources.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/crypto.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/crypto.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/ecdh.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/ecdh.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/ecdh.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/ecdh.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_driver.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_driver.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_internal.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_internal.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/hci_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/radio_nrf5_txp.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/controller/radio_nrf5_txp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/hci_vs_sdc.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/hci_vs_sdc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/host_extensions/hci_types_host_extensions.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/host_extensions/hci_types_host_extensions.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/host_extensions/host_extensions.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/bluetooth/host_extensions/host_extensions.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/caf/events/led_event.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/caf/events/led_event.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/caf/events/module_state_event.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/caf/events/module_state_event.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/caf/modules/leds.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/caf/modules/leds.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/mpsl/fem/api_init/mpsl_fem_api_init.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/mpsl/fem/api_init/mpsl_fem_api_init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/mpsl/init/mpsl_init.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/mpsl/init/mpsl_init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/include/mbedtls/platform.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/include/mbedtls/platform.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/md_ext.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/md_ext.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_events.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_events.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_events.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_events.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_mutexes.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_mutexes.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_mutexes.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/utils/nrf_security_mutexes.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/partition_manager/flash_map_partition_manager.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/partition_manager/flash_map_partition_manager.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#LICENSEREF-NORDIC-5-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90">nrfconnect/sdk-nrfxlib</a>
Version:    342901a77938defa0edfa045f9bd90350958ae90</div><a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_abort.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_abort.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_defines.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_defines.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_mutex.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/include/nrf_cc3xx_platform_mutex.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/src/nrf_cc3xx_platform_abort_zephyr.c" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/src/nrf_cc3xx_platform_abort_zephyr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/src/nrf_cc3xx_platform_no_mutex_zephyr.c" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_cc312_platform/src/nrf_cc3xx_platform_no_mutex_zephyr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_ecdh_p256.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_ecdh_p256.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_sha512.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_sha512.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_types.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/crypto/nrf_oberon/include/ocrypto_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/include/mpsl_fem_init.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/fem/include/mpsl_fem_init.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_ecb.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_ecb.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_timeslot.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/mpsl_timeslot.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/nrf_errno.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/include/nrf_errno.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/lib/nrf53/soft-float/libmpsl.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/mpsl/lib/nrf53/soft-float/libmpsl.a</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_controller_baseband.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_controller_baseband.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_info_params.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_info_params.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_le.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_le.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_link_control.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_link_control.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_status_params.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_cmd_status_params.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_vs.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_hci_vs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_soc.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/sdc_soc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/lib/nrf53/soft-float/libsoftdevice_controller_multirole.a" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/lib/nrf53/soft-float/libsoftdevice_controller_multirole.a</a>
</pre>
	
	
	
	<h2 id="LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">LicenseRef-west-ncs-sbom-BSD-1-Clause-SEGGER</h2>
	<div>
		
			
				Non-standard SPDX License:
			
			
				BSD 1-Clause License (SEGGER-Specific)
			
			
			
				<br /><a href="#text-LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">The license text</a> is added to this report.
			
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/SEGGER#B011C45B585E097D95D9CF93EDF4F2E01588D3CD#LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/SEGGER#B011C45B585E097D95D9CF93EDF4F2E01588D3CD">zephyrproject-rtos/segger</a>
Version:    b011c45b585e097d95d9cf93edf4f2e01588d3cd</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/debug/segger/Config/SEGGER_RTT_Conf.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/debug/segger/Config/SEGGER_RTT_Conf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/debug/segger/SEGGER/SEGGER_RTT.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/debug/segger/SEGGER/SEGGER_RTT.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/debug/segger/SEGGER/SEGGER_RTT.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/debug/segger/SEGGER/SEGGER_RTT.h</a>
</pre>
	
	
	
	<h2 id="LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</h2>
	<div>
		
			
				Non-standard SPDX License:
			
			
				Variant of BSD-3-Clause license with &#34;Intel Corporation&#34; as a copyright holder.
			
			
			
				<br /><a href="#text-LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">The license text</a> is added to this report.
			
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot</a>
Version:    4594a8693738004af89929cad12d33cdc82fbe6c</div><a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/constants.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/constants.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/sha256.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/sha256.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/utils.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/include/tinycrypt/utils.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/sha256.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/sha256.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/utils.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/tinycrypt/lib/source/utils.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/TINYCRYPT#1012A3EBEE18C15EDE5EFC8332EE2FC37817670F#LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/TINYCRYPT#1012A3EBEE18C15EDE5EFC8332EE2FC37817670F">zephyrproject-rtos/tinycrypt</a>
Version:    1012a3ebee18c15ede5efc8332ee2fc37817670f</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/aes.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/aes.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/ccm_mode.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/ccm_mode.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/cmac_mode.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/cmac_mode.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/constants.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/constants.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/hmac.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/hmac.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/hmac_prng.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/hmac_prng.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/sha256.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/sha256.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/utils.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/include/tinycrypt/utils.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/aes_decrypt.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/aes_decrypt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/aes_encrypt.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/aes_encrypt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/cmac_mode.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/cmac_mode.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/hmac.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/hmac.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/hmac_prng.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/hmac_prng.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/sha256.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/sha256.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/utils.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/tinycrypt/lib/source/utils.c</a>
</pre>
	
	
	
	<h2 id="NORDIC-5-CLAUSE">NORDIC-5-CLAUSE</h2>
	<div>
		
			
				Non-standard SPDX License:
			
			
				NORDIC-5-CLAUSE
			
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#NORDIC-5-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90">nrfconnect/sdk-nrfxlib</a>
Version:    342901a77938defa0edfa045f9bd90350958ae90</div><a href="file:////opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/nrf_errno.h" class="fa">/opt/nordic/ncs/v2.8.0/nrfxlib/softdevice_controller/include/nrf_errno.h</a>
</pre>
	
	
	
	<h2 id="APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</h2>
	<div>
		
			The license expression
			
			
				containing the following licenses:<ul>
				
					<li>
					Apache-2.0<br />
					
					
						Apache License 2.0
					
					
						<br /><a href="https://spdx.org/licenses/Apache-2.0.html">https://spdx.org/licenses/Apache-2.0.html</a>
					
					
					</li>
				
					<li>
					GPL-2.0-or-later<br />
					
					
						GNU General Public License v2.0 or later
					
					
						<br /><a href="https://spdx.org/licenses/GPL-2.0-or-later.html">https://spdx.org/licenses/GPL-2.0-or-later.html</a>
					
					
					</li>
				
			
			</ul>
		
	</div>
	<pre class="list">
<div class="package" id="package##APACHE-2.0 OR GPL-2.0-OR-LATER">Repository: Unknown
Version:    Unknown</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/mbedtls/build_info.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/mbedtls/build_info.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/mbedtls/config_psa.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/mbedtls/config_psa.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/build_info.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/build_info.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_auto_enabled.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_auto_enabled.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_config_key_pair_types.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_config_key_pair_types.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_config_synonyms.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_adjust_config_synonyms.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_compat.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_compat.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_common.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_common.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_composites.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_composites.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_key_derivation.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_key_derivation.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_primitives.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_driver_contexts_primitives.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_extra.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_extra.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_legacy.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_legacy.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_platform.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_platform.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_sizes.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_sizes.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_struct.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_struct.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_types.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_types.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_values.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/crypto_values.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MBEDTLS#98603A8C91660BEAC00E0EE1D76198FB7C4ED29B#APACHE-2.0 OR GPL-2.0-OR-LATER">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MBEDTLS#98603A8C91660BEAC00E0EE1D76198FB7C4ED29B">nrfconnect/sdk-mbedtls</a>
Version:    98603a8c91660beac00e0ee1d76198fb7c4ed29b</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/library/alignment.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/library/alignment.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/library/common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/library/common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/asn1.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/asn1.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/asn1write.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/asn1write.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/base64.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/base64.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/bignum.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/bignum.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/block_cipher.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/block_cipher.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/check_config.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/check_config.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/cipher.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/cipher.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_ssl.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_ssl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_x509.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/config_adjust_x509.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/constant_time.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/constant_time.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/ecdsa.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/ecdsa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/ecp.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/ecp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/entropy.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/entropy.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/error.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/error.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/md.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/md.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/oid.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/oid.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/pk.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/pk.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/platform_util.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/platform_util.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/private_access.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/private_access.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/psa_util.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/psa_util.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/rsa.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/rsa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/threading.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/include/mbedtls/threading.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aes.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aes.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aesce.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aesce.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aesni.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/aesni.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/asn1parse.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/asn1parse.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/asn1write.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/asn1write.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/base64.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/base64.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/base64_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/base64_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/bignum.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/bignum.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/bignum_core.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/bignum_core.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/block_cipher.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/block_cipher.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/block_cipher_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/block_cipher_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ccm.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ccm.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/chachapoly.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/chachapoly.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cipher.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cipher.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cipher_wrap.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cipher_wrap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cmac.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/cmac.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/constant_time.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/constant_time.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ctr_drbg.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ctr_drbg.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/dhm.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/dhm.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecdh.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecdh.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp_curves.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp_curves.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp_curves_new.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/ecp_curves_new.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/entropy.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/entropy.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/gcm.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/gcm.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/hkdf.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/hkdf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/hmac_drbg.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/hmac_drbg.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/md.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/md.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/md5.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/md5.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/nist_kw.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/nist_kw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/oid.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/oid.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/padlock.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/padlock.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pem.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pem.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_ecc.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_ecc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_wrap.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pk_wrap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkcs12.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkcs12.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkcs5.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkcs5.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkparse.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkparse.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkwrite.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/pkwrite.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/platform.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/platform.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/psa_util.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/psa_util.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/rsa.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/rsa.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/rsa_alt_helpers.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/rsa_alt_helpers.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha1.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha1.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha256.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha256.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha3.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha3.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha512.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/sha512.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/version.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/mbedtls/library/version.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#APACHE-2.0 OR GPL-2.0-OR-LATER">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf</a>
Version:    a2386bfc84016fa571f997ac871b25bd67ca481a</div><a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/ecdsa_oberon.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/ecdsa_oberon.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395#APACHE-2.0 OR GPL-2.0-OR-LATER">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395">nrfconnect/sdk-oberon-psa-crypto</a>
Version:    b41e899e7302462eb952b0b6a7c6903e368fb395</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/alignment.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/alignment.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/constant_time_impl.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/constant_time_impl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/constant_time_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/constant_time_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/oberon_psa_common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/oberon_psa_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/platform.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/platform.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/platform_util.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/platform_util.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/psa_util_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/library/psa_util_internal.h</a>
</pre>
	
	
	
	<h2 id="GPL-3.0-OR-LATER WITH GCC-EXCEPTION-3.1">GPL-3.0-or-later WITH GCC-EXCEPTION-3.1</h2>
	<div>
		
			The license expression
			
			
				containing the following licenses:<ul>
				
					<li>
					GPL-3.0-or-later<br />
					
					
						GNU General Public License v3.0 or later
					
					
						<br /><a href="https://spdx.org/licenses/GPL-3.0-or-later.html">https://spdx.org/licenses/GPL-3.0-or-later.html</a>
					
					
					</li>
				
			
			</ul>
		
	</div>
	<pre class="list">
<div class="package" id="package##GPL-3.0-OR-LATER WITH GCC-EXCEPTION-3.1">Repository: Unknown
Version:    Unknown</div><a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/arm_cmse.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/arm_cmse.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/float.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/float.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdalign.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdalign.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdarg.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdarg.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdbool.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stdbool.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stddef.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include/stddef.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include-fixed/limits.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/lib/gcc/arm-zephyr-eabi/12.2.0/include-fixed/limits.h</a>
</pre>
	
	
	
	<h2 id="APACHE-2.0">Apache-2.0</h2>
	<div>
		
			
			
				Apache License 2.0
			
			
				<br /><a href="https://spdx.org/licenses/Apache-2.0.html">https://spdx.org/licenses/Apache-2.0.html</a>
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package##APACHE-2.0">Repository: Unknown
Version:    Unknown</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/misc/generated/configs.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/zephyr/misc/generated/configs.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/misc/generated/configs.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/zephyr/misc/generated/configs.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/misc/generated/configs.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/mcuboot/zephyr/misc/generated/configs.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot</a>
Version:    4594a8693738004af89929cad12d33cdc82fbe6c</div><a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_hooks.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_hooks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_public_hooks.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_public_hooks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_record.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_record.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_status.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/boot_status.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil_log.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil_log.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil_public.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/bootutil_public.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/caps.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/caps.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/common.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/ecdsa.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/ecdsa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/sha.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/crypto/sha.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/fault_injection_hardening.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/fault_injection_hardening.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/ignore.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/ignore.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/image.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/image.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/mcuboot_status.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/mcuboot_status.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/ramload.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/ramload.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/security_cnt.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/security_cnt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/sign_key.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/include/bootutil/sign_key.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_misc.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_misc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_misc.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_misc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_public.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/bootutil_public.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/caps.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/caps.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/encrypted.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/encrypted.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/fault_injection_hardening.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/fault_injection_hardening.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_ecdsa.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_ecdsa.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_ed25519.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_ed25519.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_rsa.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_rsa.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_validate.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/image_validate.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/loader.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/loader.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_misc.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_misc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_move.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_move.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_scratch.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/swap_scratch.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/tlv.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/bootutil/src/tlv.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/arm_cleanup.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/arm_cleanup.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/flash_map_extended.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/flash_map_extended.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/arm_cleanup.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/arm_cleanup.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/config-asn1.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/config-asn1.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/flash_map_backend/flash_map_backend.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/flash_map_backend/flash_map_backend.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/io/io.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/io/io.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot-mbedtls-cfg.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot-mbedtls-cfg.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot_config/mcuboot_config.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot_config/mcuboot_config.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot_config/mcuboot_logging.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/mcuboot_config/mcuboot_logging.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/os/os_heap.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/os/os_heap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/os/os_malloc.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/os/os_malloc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/sysflash/sysflash.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/sysflash/sysflash.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/target.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/include/target.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/io.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/io.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/keys.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/keys.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/main.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/main.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/os.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/boot/zephyr/os.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/common.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/asn1.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/asn1.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/bignum.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/bignum.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/build_info.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/build_info.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/check_config.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/check_config.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/ecdsa.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/ecdsa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/ecp.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/ecp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/error.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/error.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/md.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/md.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/oid.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/oid.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/pk.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/pk.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/platform.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/platform.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/platform_util.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/platform_util.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/private_access.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/private_access.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/threading.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/threading.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/version.h" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/include/mbedtls/version.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/src/asn1parse.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/src/asn1parse.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/src/platform_util.c" class="fa">/opt/nordic/ncs/v2.8.0/bootloader/mcuboot/ext/mbedtls-asn1/src/platform_util.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf</a>
Version:    a2386bfc84016fa571f997ac871b25bd67ca481a</div><a href="file:////opt/nordic/ncs/v2.8.0/nrf/modules/trusted-firmware-m/fault.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/modules/trusted-firmware-m/fault.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/ecjpake_oberon.c" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/src/legacy/ecjpake_oberon.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395">nrfconnect/sdk-oberon-psa-crypto</a>
Version:    b41e899e7302462eb952b0b6a7c6903e368fb395</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/include/psa/crypto_driver_config.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/crypto/oberon-psa-crypto/include/psa/crypto_driver_config.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr</a>
Version:    0bc3393fb112ec80ebeab48cd023d69b1e9db757</div><a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/__aeabi_read_tp.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/__aeabi_read_tp.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/cmse/arm_core_cmse.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/cmse/arm_core_cmse.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/cpu_idle.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/cpu_idle.c</a> <span class="modifications">with local modifications</span>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/exc_exit.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/exc_exit.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fault.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fault.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fault_s.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fault_s.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fpu.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/fpu.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/irq_init.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/irq_init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/irq_manage.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/irq_manage.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/isr_wrapper.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/isr_wrapper.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/prep_c.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/prep_c.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/reset.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/reset.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/scb.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/scb.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/swap.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/swap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/swap_helper.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/swap_helper.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/thread.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/thread.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/thread_abort.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/thread_abort.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/vector_table.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/vector_table.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/vector_table.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/cortex_m/vector_table.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/fatal.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/fatal.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_core_mpu.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_core_mpu.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_core_mpu_dev.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_core_mpu_dev.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu_regions.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu_regions.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu_v8_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/mpu/arm_mpu_v8_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/nmi.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/nmi.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/nmi_on_reset.S" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/nmi_on_reset.S</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/offsets/offsets.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/offsets/offsets.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/offsets/offsets_aarch32.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/offsets/offsets_aarch32.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/tls.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/core/tls.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/cmse.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/cmse.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/exception.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/exception.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/kernel_arch_func.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/kernel_arch_func.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/stack.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/cortex_m/stack.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/kernel_arch_data.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/kernel_arch_data.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/kernel_arch_func.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/kernel_arch_func.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/offsets_short_arch.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/arm/include/offsets_short_arch.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/common/include/sw_isr_common.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/common/include/sw_isr_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/common/isr_tables.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/common/isr_tables.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/arch/common/sw_isr_common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/arch/common/sw_isr_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_context.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_context.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_nrfx_saadc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/adc/adc_nrfx_saadc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/bluetooth/hci/ipc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/bluetooth/hci/ipc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/bluetooth/hci/nrf53_support.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/bluetooth/hci/nrf53_support.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/clock_control_nrf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/clock_control_nrf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/nrf_clock_calibration.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/nrf_clock_calibration.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/nrf_clock_calibration.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/clock_control/nrf_clock_calibration.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/entropy/entropy_nrf5.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/entropy/entropy_nrf5.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/flash_page_layout.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/flash_page_layout.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/flash_util.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/flash_util.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/soc_flash_nrf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/soc_flash_nrf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/soc_flash_nrf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/flash/soc_flash_nrf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/gpio/gpio_nrfx.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/gpio/gpio_nrfx.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/hwinfo/hwinfo_nrf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/hwinfo/hwinfo_nrf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/hwinfo/hwinfo_weak_impl.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/hwinfo/hwinfo_weak_impl.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/i2c/i2c_common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/i2c/i2c_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/i2c/i2c_nrfx_twim.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/i2c/i2c_nrfx_twim.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/led/led_pwm.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/led/led_pwm.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/mbox/mbox_nrfx_ipc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/mbox/mbox_nrfx_ipc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/pinctrl/common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/pinctrl/common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/pinctrl/pinctrl_nrf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/pinctrl/pinctrl_nrf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/pwm/pwm_nrfx.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/pwm/pwm_nrfx.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor_calc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/sensor/ntc_thermistor/ntc_thermistor_calc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_context.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_context.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_common.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_spim.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/spi/spi_nrfx_spim.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/timer/nrf_rtc_timer.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/timer/nrf_rtc_timer.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/drivers/timer/sys_clock_init.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/drivers/timer/sys_clock_init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/app_memory/app_memdomain.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/app_memory/app_memdomain.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/app_memory/mem_domain.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/app_memory/mem_domain.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arch_inlines.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arch_inlines.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arch_interface.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arch_interface.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/arch.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/arch.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/arch_inlines.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/arch_inlines.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/asm_inline.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/asm_inline.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/asm_inline_gcc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/asm_inline_gcc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/barrier.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/barrier.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/arm_mpu_mem_cfg.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/arm_mpu_mem_cfg.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/cpu.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/cpu.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/exception.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/exception.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/fpu.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/fpu.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/memory_map.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/memory_map.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/nvic.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/cortex_m/nvic.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/error.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/error.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/exception.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/exception.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/irq.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/irq.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/misc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/misc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/mpu/arm_mpu.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/mpu/arm_mpu.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/mpu/arm_mpu_v8.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/mpu/arm_mpu_v8.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/nmi.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/nmi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/structs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/structs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/syscall.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/syscall.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/thread.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/arm/thread.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/cache.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/cache.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/addr_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/addr_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/exc_handle.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/exc_handle.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/ffs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/ffs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/sys_bitops.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/sys_bitops.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/sys_io.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/common/sys_io.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/cpu.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/cpu.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/exception.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/exception.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/structs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/structs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/syscall.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/arch/syscall.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/addr.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/addr.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/att.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/att.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/bluetooth.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/bluetooth.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/buf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/buf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/byteorder.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/byteorder.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/conn.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/conn.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/controller.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/controller.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/crypto.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/crypto.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/direction.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/direction.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/gap.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/gap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/gatt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/gatt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_raw.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_raw.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_vs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/hci_vs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/iso.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/iso.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/l2cap.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/l2cap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/services/bas.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/services/bas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/uuid.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/bluetooth/uuid.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/cache.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/cache.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/coredump.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/coredump.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/gcov.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/gcov.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/object_tracing.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/object_tracing.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/sparse.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/sparse.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/stack.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/debug/stack.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/device.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/device.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/can.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/can.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/clocks.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/clocks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/dma.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/dma.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/fixed-partitions.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/fixed-partitions.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/io-channels.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/io-channels.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/mbox.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/mbox.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/ordinals.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/ordinals.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/pinctrl.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/pinctrl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/pwms.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/pwms.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/reset.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/reset.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/spi.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree/spi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/devicetree.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dfu/flash_img.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dfu/flash_img.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dfu/mcuboot.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dfu/mcuboot.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/adc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/adc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/bluetooth/hci_driver.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/bluetooth/hci_driver.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/bluetooth.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/bluetooth.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/clock_control/nrf_clock_control.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/clock_control/nrf_clock_control.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/clock_control.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/clock_control.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/dac.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/dac.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/entropy.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/entropy.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/flash.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/flash.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/gpio/gpio_utils.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/gpio/gpio_utils.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/hwinfo.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/hwinfo.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/i2c.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/i2c.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/led.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/led.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/led_strip.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/led_strip.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/mbox.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/mbox.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/pinctrl.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/pinctrl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/pwm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/pwm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/sensor.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/sensor.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/sensor_data_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/sensor_data_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/spi/rtio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/spi/rtio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/spi.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/spi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/timer/nrf_rtc_timer.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/timer/nrf_rtc_timer.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/timer/system_timer.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/timer/system_timer.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/usb/usb_dc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/drivers/usb/usb_dc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dsp/print_format.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dsp/print_format.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dsp/types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dsp/types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/adc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/adc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-nrf54l.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-nrf54l.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/dt-util.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/dt-util.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/gpio/gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/gpio/gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/gpio/nordic-nrf-gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/gpio/nordic-nrf-gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/i2c/i2c.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/i2c/i2c.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/ipc_service/static_vrings.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/ipc_service/static_vrings.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/led/led.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/led/led.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/memory-attr/memory-attr-arm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/memory-attr/memory-attr-arm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/memory-attr/memory-attr.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/memory-attr/memory-attr.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/power/nordic-nrf-gpd.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/power/nordic-nrf-gpd.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/pwm/pwm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/pwm/pwm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/regulator/nrf5x.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/regulator/nrf5x.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/spi/spi.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/dt-bindings/spi/spi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fatal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fatal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fatal_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fatal_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fs/nvs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/fs/nvs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/init.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/init.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/internal/syscall_handler.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/internal/syscall_handler.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_rpmsg.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_rpmsg.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_service.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_service.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_service_backend.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_service_backend.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_static_vrings.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/ipc/ipc_static_vrings.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq_multilevel.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq_multilevel.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq_offload.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/irq_offload.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/internal/mm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/internal/mm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/internal/smp.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/internal/smp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/mm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/mm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/obj_core.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/obj_core.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/stats.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/stats.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/thread.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/thread.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/thread_stack.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel/thread_stack.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_includes.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_includes.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_structs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_structs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_version.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/kernel_version.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/devicetree_regions.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/devicetree_regions.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/linker-defs.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/linker-defs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/section_tags.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/section_tags.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/sections.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/sections.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/utils.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/linker/utils.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/llext/symbol.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/llext/symbol.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_backend.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_backend.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_backend_std.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_backend_std.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_core.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_core.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_ctrl.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_ctrl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_frontend.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_frontend.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_instance.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_instance.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_link.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_link.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_msg.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_msg.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output_custom.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output_custom.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output_dict.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/logging/log_output_dict.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/math/ilog2.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/math/ilog2.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mem_mgmt/mem_attr.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mem_mgmt/mem_attr.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/grp/img_mgmt/img_mgmt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/grp/img_mgmt/img_mgmt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/grp/os_mgmt/os_mgmt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/grp/os_mgmt/os_mgmt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/handlers.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/handlers.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/mgmt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/mgmt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/mgmt_defines.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/mgmt/mgmt_defines.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/smp/smp.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/smp/smp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/smp/smp_client.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/smp/smp_client.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/transport/smp.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/transport/smp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/transport/smp_bt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/mgmt/mcumgr/transport/smp_bt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/net_buf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/net_buf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/platform/hooks.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/platform/hooks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/device.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/device.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/device_runtime.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/device_runtime.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/pm.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/pm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/state.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/pm/state.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/posix/posix_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/posix/posix_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/random/random.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/random/random.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/rtio/rtio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/rtio/rtio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/settings/settings.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/settings/settings.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_fprintf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_fprintf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_history.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_history.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_log_backend.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_log_backend.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_string_conv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_string_conv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/shell/shell_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/spinlock.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/spinlock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/stats/stats.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/stats/stats.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/storage/flash_map.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/storage/flash_map.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/storage/stream_flash.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/storage/stream_flash.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sw_isr_table.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sw_isr_table.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/__assert.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/__assert.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic_builtin.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic_builtin.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/atomic_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/barrier.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/barrier.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/bitarray.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/bitarray.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/byteorder.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/byteorder.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_cxx.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_cxx.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_enums.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_enums.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/cbprintf_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/check.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/check.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/crc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/crc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/device_mmio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/device_mmio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/dlist.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/dlist.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/errno_private.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/errno_private.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/heap_listener.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/heap_listener.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/internal/kobject_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/internal/kobject_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/iterable_sections.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/iterable_sections.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/kobject.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/kobject.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/libc-hooks.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/libc-hooks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/list_gen.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/list_gen.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/math_extras.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/math_extras.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/math_extras_impl.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/math_extras_impl.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_blocks.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_blocks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_manage.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_manage.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_stats.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mem_stats.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_lockfree.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_lockfree.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_packet.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_packet.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_pbuf.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mpsc_pbuf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mutex.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/mutex.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/notify.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/notify.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/onoff.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/onoff.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/printk-hooks.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/printk-hooks.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/printk.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/printk.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/rb.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/rb.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/reboot.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/reboot.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/ring_buffer.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/ring_buffer.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sem.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sem.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sflist.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sflist.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/slist.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/slist.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sys_heap.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sys_heap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sys_io.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/sys_io.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/time_units.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/time_units.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/timeutil.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/timeutil.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_is_eq.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_is_eq.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_dec.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_dec.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_inc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_inc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_x2.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_internal_util_x2.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_listify.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_listify.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_loops.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_loops.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_macro.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys/util_macro.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/sys_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/syscall.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/syscall.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/timing/timing.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/timing/timing.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/timing/types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/timing/types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/common.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/gcc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/gcc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/zephyr_stdint.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain/zephyr_stdint.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/toolchain.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing_macros.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing_macros.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing_syscall.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracing_syscall.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracking.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/tracing/tracking.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/class/usb_hub.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/class/usb_hub.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/usb_ch9.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/usb_ch9.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/banner.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/banner.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/busy_wait.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/busy_wait.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/condvar.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/condvar.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/device.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/device.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/dynamic_disabled.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/dynamic_disabled.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/errno.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/errno.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/events.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/events.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/fatal.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/fatal.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/float.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/float.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/idle.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/idle.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/gen_offset.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/gen_offset.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/ipi.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/ipi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_arch_interface.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_arch_interface.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_offsets.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_offsets.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_tls.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kernel_tls.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/ksched.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/ksched.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kswap.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kswap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kthread.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/kthread.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/offsets_short.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/offsets_short.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/priority_q.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/priority_q.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/timeout_q.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/timeout_q.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/include/wait_q.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/include/wait_q.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/init.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/init_static.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/init_static.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/kheap.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/kheap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/mailbox.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/mailbox.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/main_weak.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/main_weak.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/mem_slab.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/mem_slab.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/mempool.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/mempool.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/msg_q.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/msg_q.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/mutex.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/mutex.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/nothread.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/nothread.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/poll.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/poll.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/priority_queues.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/priority_queues.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/queue.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/queue.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/sched.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/sched.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/sem.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/sem.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/spinlock_validate.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/spinlock_validate.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/stack.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/stack.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/system_work_q.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/system_work_q.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/thread.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/thread.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/thread_monitor.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/thread_monitor.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/timeout.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/timeout.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/timer.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/timer.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/timeslicing.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/timeslicing.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/version.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/version.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/work.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/work.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/kernel/xip.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/kernel/xip.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc16_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc16_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc24_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc24_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc32_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc32_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc32c_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc32c_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc4_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc4_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc7_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc7_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc8_sw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/crc/crc8_sw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/heap/heap.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/heap/heap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/heap/heap.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/heap/heap.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/stdlib/abort.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/stdlib/abort.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/stdlib/malloc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/stdlib/malloc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/string/strnlen.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/string/strnlen.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/asctime.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/asctime.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/ctime.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/ctime.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/gmtime_r.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/gmtime_r.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/localtime_r_utc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/common/source/time/localtime_r_utc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/assert.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/assert.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/ctype.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/ctype.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/errno.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/errno.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/inttypes.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/inttypes.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/limits.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/limits.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/math.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/math.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdbool.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdbool.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdint.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdint.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdio.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdlib.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/stdlib.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/string.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/string.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/_timespec.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/_timespec.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/_types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/_types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/types.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/sys/types.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/time.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/include/time.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/math/sqrt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/math/sqrt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/math/sqrtf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/math/sqrtf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/bsearch.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/bsearch.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/exit.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/exit.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/qsort.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/qsort.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/fprintf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/fprintf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/sprintf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/sprintf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/stdout_console.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdout/stdout_console.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strerror.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strerror.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/string.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/string.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strncasecmp.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strncasecmp.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strspn.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strspn.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/time/gmtime.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/time/gmtime.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/picolibc/libc-hooks.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/picolibc/libc-hooks.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/net_buf/buf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/net_buf/buf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/net_buf/buf_simple.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/net_buf/buf_simple.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/assert.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/assert.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_complete.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_complete.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_nano.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_nano.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_packaged.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/cbprintf_packaged.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/mpsc_pbuf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/mpsc_pbuf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/printk.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/printk.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/reboot.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/reboot.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/sem.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/sem.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/os/thread_entry.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/os/thread_entry.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/bitarray.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/bitarray.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/dec.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/dec.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/hex.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/hex.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/notify.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/notify.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/onoff.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/onoff.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/rb.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/rb.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/utils/timeutil.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/utils/timeutil.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core_m.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core_m.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core_m_defaults.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/cmsis/cmsis_core_m_defaults.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_common.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_nrf5340_application.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_nrf5340_application.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_nrf5340_network.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_config_nrf5340_network.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_glue.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_glue.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_glue.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_glue.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_log.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/hal_nordic/nrfx/nrfx_log.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/segger/SEGGER_RTT_zephyr.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/segger/SEGGER_RTT_zephyr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/trusted-firmware-m/interface/interface.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/trusted-firmware-m/interface/interface.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/modules/trusted-firmware-m/src/zephyr_tfm_log.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/modules/trusted-firmware-m/src/zephyr_tfm_log.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/pinctrl_soc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/pinctrl_soc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_nrf_common.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_nrf_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_secure.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_secure.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_secure.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/common/soc_secure.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/nrf53_cpunet_mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/nrf53_cpunet_mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/nrf53_cpunet_mgmt.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/nrf53_cpunet_mgmt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc_cpu_idle.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/soc_cpu_idle.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/sync_rtc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/nrf53/sync_rtc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_base_addresses.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_base_addresses.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_binding_headers.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_binding_headers.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_enabled_instances.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/soc/nordic/validate_enabled_instances.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/addr.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/addr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/assert.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/assert.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/bt_str.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/bt_str.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/bt_str.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/bt_str.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/dummy.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/dummy.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/rpa.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/rpa.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/rpa.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/common/rpa.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/controller/ll_sw/nordic/hal/nrf5/debug.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/controller/ll_sw/nordic/hal/nrf5/debug.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto_tc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/crypto/bt_crypto_tc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/addr.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/addr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/addr_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/addr_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/adv.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/adv.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/adv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/adv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/att.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/att.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/att_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/att_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/buf.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/buf.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/buf_view.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/buf_view.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/l2cap_br_interface.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/l2cap_br_interface.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/sco_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/sco_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/ssp.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/classic/ssp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/conn.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/conn.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/conn_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/conn_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/crypto.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/crypto.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/crypto_tc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/crypto_tc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/data.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/data.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/direction_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/direction_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/ecc.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/ecc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/ecc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/ecc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/gatt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/gatt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/gatt_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/gatt_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_common.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_core.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_core.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_core.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_core.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_ecc.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_ecc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_raw.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_raw.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_raw_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/hci_raw_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/id.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/id.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/id.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/id.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/iso_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/iso_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/keys.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/keys.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/keys.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/keys.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/l2cap.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/l2cap.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/l2cap_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/l2cap_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/long_wq.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/long_wq.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/long_wq.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/long_wq.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/monitor.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/monitor.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/scan.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/scan.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/settings.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/settings.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/settings.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/settings.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/smp.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/smp.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/smp.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/smp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/uuid.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/host/uuid.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/bas/bas.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/bas/bas.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/bas/bas_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/bas/bas_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/dis.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/bluetooth/services/dis.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/debug/thread_info.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/debug/thread_info.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/boot/mcuboot.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/boot/mcuboot.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/boot/mcuboot_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/boot/mcuboot_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/img_util/flash_img.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/dfu/img_util/flash_img.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/fs/nvs/nvs.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/fs/nvs/nvs.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/fs/nvs/nvs_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/fs/nvs/nvs_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/backends/ipc_rpmsg_static_vrings.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/ipc_service.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/ipc_service.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/lib/ipc_rpmsg.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/lib/ipc_rpmsg.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/lib/ipc_static_vrings.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/ipc/ipc_service/lib/ipc_static_vrings.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/backends/log_backend_rtt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/backends/log_backend_rtt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_cache.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_cache.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_cache.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_cache.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_core.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_core.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_minimal.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_minimal.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_msg.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_msg.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_output.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/logging/log_output.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mem_mgmt/mem_attr.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mem_mgmt/mem_attr.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/bootutil_hooks/nrf53_hooks.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/bootutil_hooks/nrf53_hooks.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/include/mgmt/mcumgr/grp/img_mgmt/img_mgmt_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/include/mgmt/mcumgr/grp/img_mgmt/img_mgmt_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt_state.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt_state.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt_util.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/img_mgmt_util.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/zephyr_img_mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/img_mgmt/src/zephyr_img_mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/os_mgmt/src/os_mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/grp/os_mgmt/src/os_mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/mgmt/src/mgmt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/mgmt/src/mgmt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/smp/src/smp.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/smp/src/smp.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/include/mgmt/mcumgr/transport/smp_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/include/mgmt/mcumgr/transport/smp_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/include/mgmt/mcumgr/transport/smp_reassembly.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/include/mgmt/mcumgr/transport/smp_reassembly.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp_bt.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp_bt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp_reassembly.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/transport/src/smp_reassembly.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/util/include/mgmt/mcumgr/util/zcbor_bulk.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/util/include/mgmt/mcumgr/util/zcbor_bulk.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/util/src/zcbor_bulk.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/mgmt/mcumgr/util/src/zcbor_bulk.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/random/random_entropy_device.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/random/random_entropy_device.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/include/settings/settings_file.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/include/settings/settings_file.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_init.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_line.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_line.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_none.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_none.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_runtime.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_runtime.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_store.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/settings/src/settings_store.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map_layout.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map_layout.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map_priv.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/flash_map/flash_map_priv.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/stream/stream_flash.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/storage/stream/stream_flash.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/subsys/tracing/tracing_none.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/subsys/tracing/tracing_none.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6">SolucionesKenko/juno</a>
Version:    1ecd7f8c80f068d5f13c466532abe48230701ee6</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/ad8801.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/controllers/ad8801.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/heat_control.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/src/modules/therapy/heat_control.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/CMSIS#4B96CBB174678DCD3CA86E11E1F24BC5F8726DA0#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/CMSIS#4B96CBB174678DCD3CA86E11E1F24BC5F8726DA0">zephyrproject-rtos/cmsis</a>
Version:    4b96cbb174678dcd3ca86e11e1f24bc5f8726da0</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_compiler.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_compiler.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_gcc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_gcc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_version.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/cmsis_version.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/core_cm33.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/core_cm33.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/mpu_armv8.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/cmsis/CMSIS/Core/Include/mpu_armv8.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717">zephyrproject-rtos/hal_nordic</a>
Version:    5c8d109371ebb740fbef1f440a3b59e488a36717</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf53.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf53.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_application.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_application.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_application.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_application.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf5340_network.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf53_approtect.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/system_nrf53_approtect.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/ZCBOR#47F34DD7F5284E8750B5A715DEE7F77C6C5BDC3F#APACHE-2.0">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/ZCBOR#47F34DD7F5284E8750B5A715DEE7F77C6C5BDC3F">zephyrproject-rtos/zcbor</a>
Version:    47f34dd7f5284e8750b5a715dee7f77c6c5bdc3f</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_decode.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_decode.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_encode.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_encode.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_print.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_print.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_tags.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/include/zcbor_tags.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_common.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_common.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_decode.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_decode.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_encode.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/zcbor/src/zcbor_encode.c</a>
</pre>
	
	
	
	<h2 id="BSD-2-CLAUSE">BSD-2-Clause</h2>
	<div>
		
			
			
				BSD 2-Clause &#34;Simplified&#34; License
			
			
				<br /><a href="https://spdx.org/licenses/BSD-2-Clause.html">https://spdx.org/licenses/BSD-2-Clause.html</a>
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719#BSD-2-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719">zephyrproject-rtos/open-amp</a>
Version:    b735edbc739ad59156eb55bb8ce2583d74537719</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtqueue.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtqueue.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/virtio/virtio.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/virtio/virtio.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/virtio/virtqueue.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/virtio/virtqueue.c</a>
</pre>
	
	
	
	<h2 id="BSD-2-CLAUSE-FREEBSD">BSD-2-Clause-FreeBSD</h2>
	<div>
		
			
			
				BSD 2-Clause FreeBSD License
			
			
				<br /><a href="https://spdx.org/licenses/BSD-2-Clause-FreeBSD.html">https://spdx.org/licenses/BSD-2-Clause-FreeBSD.html</a>
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package##BSD-2-CLAUSE-FREEBSD">Repository: Unknown
Version:    Unknown</div><a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_timeval.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_timeval.h</a>
</pre>
	
	
	
	<h2 id="BSD-3-CLAUSE">BSD-3-Clause</h2>
	<div>
		
			
			
				BSD 3-Clause &#34;New&#34; or &#34;Revised&#34; License
			
			
				<br /><a href="https://spdx.org/licenses/BSD-3-Clause.html">https://spdx.org/licenses/BSD-3-Clause.html</a>
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package##BSD-3-CLAUSE">Repository: Unknown
Version:    Unknown</div><a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/alloc.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/alloc.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/assert.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/assert.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/atomic.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/atomic.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/armcc/errno.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/armcc/errno.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/atomic.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/atomic.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/compiler.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/compiler.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/compiler.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/condition.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/condition.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/cpu.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/cpu.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/device.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/device.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/dma.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/dma.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/errno.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/errno.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/io.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/io.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/irq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/irq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/irq_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/irq_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/list.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/list.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/log.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/log.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/mutex.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/mutex.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/processor/generic/cpu.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/processor/generic/cpu.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/shmem.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/shmem.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/sleep.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/sleep.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/softirq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/softirq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/spinlock.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/spinlock.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/sys.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/sys.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/alloc.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/alloc.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/assert.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/assert.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/condition.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/condition.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/io.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/io.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/irq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/irq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/log.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/log.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/mutex.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/mutex.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sleep.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sleep.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sys.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sys.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/time.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/time.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/utilities.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/libmetal/libmetal/lib/include/metal/utilities.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/open-amp/open-amp/include/generated/openamp/version_def.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/ipc_radio/modules/open-amp/open-amp/include/generated/openamp/version_def.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/alloc.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/alloc.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/assert.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/assert.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/atomic.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/atomic.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/armcc/errno.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/armcc/errno.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/atomic.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/atomic.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/compiler.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler/gcc/compiler.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/compiler.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/condition.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/condition.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/config.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/config.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/cpu.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/cpu.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/device.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/device.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/dma.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/dma.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/errno.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/errno.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/io.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/io.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/irq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/irq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/irq_controller.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/irq_controller.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/list.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/list.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/log.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/log.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/mutex.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/mutex.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/processor/generic/cpu.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/processor/generic/cpu.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/shmem.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/shmem.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/sleep.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/sleep.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/softirq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/softirq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/spinlock.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/spinlock.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/sys.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/sys.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/alloc.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/alloc.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/assert.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/assert.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/cache.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/cache.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/condition.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/condition.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/io.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/io.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/irq.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/irq.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/log.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/log.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/mutex.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/mutex.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sleep.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sleep.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sys.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/system/zephyr/sys.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/time.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/time.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/utilities.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/libmetal/libmetal/lib/include/metal/utilities.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/open-amp/open-amp/include/generated/openamp/version_def.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/modules/open-amp/open-amp/include/generated/openamp/version_def.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/client.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/client.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/error.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa/error.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa_manifest/sid.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/psa_manifest/sid.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_ioctl_core_api.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_ioctl_core_api.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_ns_interface.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_ns_interface.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_platform_api.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_platform_api.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_psa_call_pack.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_psa_call_pack.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_veneers.h" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/include/tfm_veneers.h</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_crypto_api.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_crypto_api.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_ioctl_core_ns_api.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_ioctl_core_ns_api.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_platform_api.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_platform_api.c</a>
<a href="file:////Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_tz_psa_ns_api.c" class="fa">/Users/<USER>/Documents/kenko/juno/juno/juno_fw/build/juno_fw/tfm/api_ns/interface/src/tfm_tz_psa_ns_api.c</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/math.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/machine/math.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/newlib.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/newlib.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_timespec.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/_timespec.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/cdefs.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/cdefs.h</a>
<a href="file:////opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/time.h" class="fa">/opt/nordic/ncs/toolchains/15b490767d/opt/zephyr-sdk/arm-zephyr-eabi/picolibc/include/sys/time.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#BSD-3-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf</a>
Version:    a2386bfc84016fa571f997ac871b25bd67ca481a</div><a href="file:////opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/include/tfm_crypto_defs.h" class="fa">/opt/nordic/ncs/v2.8.0/nrf/subsys/nrf_security/include/tfm_crypto_defs.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#BSD-3-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr</a>
Version:    0bc3393fb112ec80ebeab48cd023d69b1e9db757</div><a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/posix/sys/stat.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/posix/sys/stat.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/usb_device.h" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/include/zephyr/usb/usb_device.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtol.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtol.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoll.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoll.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoul.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoul.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoull.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/strtoull.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strstr.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/string/strstr.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717#BSD-3-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717">zephyrproject-rtos/hal_nordic</a>
Version:    5c8d109371ebb740fbef1f440a3b59e488a36717</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrf_bitmask.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrf_bitmask.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_dppi.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_dppi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_gpiote.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_gpiote.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_ipc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_ipc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_nvmc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_nvmc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_power_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_power_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_pwm.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_pwm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_spim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_spim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_twi_twim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_twi_twim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_twim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_twim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_wdt.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/include/nrfx_wdt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_errors.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_errors.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_ext.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_ext.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_utils.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_utils.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_utils_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/nrfx_utils_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_clock.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_dppi.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_gpiote.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_ipc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_nvmc.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_nvmc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_pwm.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_pwm.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_spim.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_spim.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_twi_twim.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_twi_twim.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_twim.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_twim.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_wdt.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/nrfx_wdt.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/prs/nrfx_prs.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/drivers/src/prs/nrfx_prs.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_cache.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_cache.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_clock.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_clock.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_dppi.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_dppi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_ficr.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_ficr.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_gpiote.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_gpiote.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_ipc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_ipc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_nvmc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_nvmc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_oscillators.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_oscillators.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_power.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_power.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_pwm.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_pwm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_regulators.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_regulators.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_reset.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_reset.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_rng.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_rng.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_rtc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_rtc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_saadc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_saadc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_spim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_spim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_spu.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_spu.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_twim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_twim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_uarte.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_uarte.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_uicr.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_uicr.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_wdt.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/hal/nrf_wdt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_common.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_common.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_dppi.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_dppi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_gpio.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_gpio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_gpiote.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_gpiote.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_pwm.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_pwm.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_rtc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_rtc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_saadc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_saadc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_spim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_spim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_twim.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_twim.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_uarte.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_uarte.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_wdt.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/haly/nrfy_wdt.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_flag32_allocator.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_gppi.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_gppi.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_gppi_dppi.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_reset_reason.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/helpers/nrfx_reset_reason.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/compiler_abstraction.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/compiler_abstraction.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf51_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf51_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf52_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf52_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_bitfields.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_bitfields.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_name_change.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_name_change.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_peripherals.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_application_peripherals.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_bitfields.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_bitfields.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_name_change.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_name_change.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_peripherals.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_network_peripherals.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_xxaa_application_memory.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_xxaa_application_memory.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_xxaa_network_memory.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf5340_xxaa_network_memory.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf53_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf53_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf54h_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf54h_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf54l_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf54l_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf91_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf91_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_erratas.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_erratas.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_mem.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_mem.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_peripherals.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/mdk/nrf_peripherals.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/nrfx.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/nrfx.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/soc/nrfx_coredep.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/nordic/nrfx/soc/nrfx_coredep.h</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/LIBMETAL#A6851BA6DBA8C9E87D00C42F171A822F7A29639B#BSD-3-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/LIBMETAL#A6851BA6DBA8C9E87D00C42F171A822F7A29639B">zephyrproject-rtos/libmetal</a>
Version:    a6851ba6dba8c9e87d00c42f171a822f7a29639b</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/device.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/device.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/dma.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/dma.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/init.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/io.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/io.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/irq.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/irq.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/log.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/log.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/shmem.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/shmem.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/softirq.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/softirq.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/alloc.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/alloc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/condition.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/condition.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/device.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/device.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/init.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/init.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/irq.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/irq.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/log.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/log.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/shmem.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/shmem.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/sys.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/sys.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/time.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/system/zephyr/time.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/version.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/hal/libmetal/libmetal/lib/version.c</a>
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719#BSD-3-CLAUSE">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719">zephyrproject-rtos/open-amp</a>
Version:    b735edbc739ad59156eb55bb8ce2583d74537719</div><a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/internal/string.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/internal/string.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/elf_loader.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/elf_loader.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/open_amp.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/open_amp.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc_loader.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc_loader.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc_virtio.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/remoteproc_virtio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/rpmsg.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/rpmsg.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/rpmsg_virtio.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/rpmsg_virtio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtio.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtio.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtio_ring.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/include/openamp/virtio_ring.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/elf_loader.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/elf_loader.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/remoteproc.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/remoteproc.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/remoteproc_virtio.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/remoteproc_virtio.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/rsc_table_parser.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/rsc_table_parser.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/rsc_table_parser.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/remoteproc/rsc_table_parser.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg_internal.h" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg_internal.h</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg_virtio.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/rpmsg/rpmsg_virtio.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/utils/string.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/utils/string.c</a>
<a href="file:////opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/version.c" class="fa">/opt/nordic/ncs/v2.8.0/modules/lib/open-amp/open-amp/lib/version.c</a>
</pre>
	
	
	
	
	
	
	
	<h2 id="MIT">MIT</h2>
	<div>
		
			
			
				MIT License
			
			
				<br /><a href="https://spdx.org/licenses/MIT.html">https://spdx.org/licenses/MIT.html</a>
			
			
		
	</div>
	<pre class="list">
<div class="package" id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#MIT">Repository: <a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr</a>
Version:    0bc3393fb112ec80ebeab48cd023d69b1e9db757</div><a href="file:////opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/atoi.c" class="fa">/opt/nordic/ncs/v2.8.0/zephyr/lib/libc/minimal/source/stdlib/atoi.c</a>
</pre>
	
	

	<h1>Packages details</h1>

	
	
	<h2 id="no-package">Unknown source</h2>
	The files from unknown source are covered by the following licenses:
	<ul>
		
		
		
		
		
		
		
		<li><a href="#package##LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
		
		
		
		
		
		
		
		
		<li><a href="#package##APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</a></li>
		
		
		
		<li><a href="#package##GPL-3.0-OR-LATER WITH GCC-EXCEPTION-3.1">GPL-3.0-or-later WITH GCC-EXCEPTION-3.1</a></li>
		
		
		
		<li><a href="#package##APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		<li><a href="#package##BSD-2-CLAUSE-FREEBSD">BSD-2-Clause-FreeBSD</a></li>
		
		
		
		<li><a href="#package##BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		
		
		<li><a href="#package##">Unknown licenses</a> that cannot be detected automatically</li>
		
	</ul>
	

	
	
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MBEDTLS#98603A8C91660BEAC00E0EE1D76198FB7C4ED29B">nrfconnect/sdk-mbedtls</h2>
	<h3>Version: 98603a8c91660beac00e0ee1d76198fb7c4ed29b</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-mbedtls">https://github.com/nrfconnect/sdk-mbedtls</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MBEDTLS#98603A8C91660BEAC00E0EE1D76198FB7C4ED29B#APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C">nrfconnect/sdk-mcuboot</h2>
	<h3>Version: 4594a8693738004af89929cad12d33cdc82fbe6c</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-mcuboot">https://github.com/nrfconnect/sdk-mcuboot</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#BSD-2-CLAUSE AND LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">BSD-2-Clause AND LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-MCUBOOT#4594A8693738004AF89929CAD12D33CDC82FBE6C#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A">nrfconnect/sdk-nrf</h2>
	<h3>Version: a2386bfc84016fa571f997ac871b25bd67ca481a</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-nrf">https://github.com/nrfconnect/sdk-nrf</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</a></li>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRF#A2386BFC84016FA571F997AC871B25BD67CA481A#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90">nrfconnect/sdk-nrfxlib</h2>
	<h3>Version: 342901a77938defa0edfa045f9bd90350958ae90</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-nrfxlib">https://github.com/nrfconnect/sdk-nrfxlib</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</a></li>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#NORDIC-5-CLAUSE">NORDIC-5-CLAUSE</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-NRFXLIB#342901A77938DEFA0EDFA045F9BD90350958AE90#">Unknown licenses</a> that cannot be detected automatically</li>
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395">nrfconnect/sdk-oberon-psa-crypto</h2>
	<h3>Version: b41e899e7302462eb952b0b6a7c6903e368fb395</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-oberon-psa-crypto">https://github.com/nrfconnect/sdk-oberon-psa-crypto</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395#APACHE-2.0 OR GPL-2.0-OR-LATER">Apache-2.0 OR GPL-2.0-or-later</a></li>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-OBERON-PSA-CRYPTO#B41E899E7302462EB952B0B6A7C6903E368FB395#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757">nrfconnect/sdk-zephyr</h2>
	<h3>Version: 0bc3393fb112ec80ebeab48cd023d69b1e9db757</h3>
	<p>URL:
	
	<a href="https://github.com/nrfconnect/sdk-zephyr">https://github.com/nrfconnect/sdk-zephyr</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#MIT">MIT</a></li>
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/NRFCONNECT/SDK-ZEPHYR#0BC3393FB112EC80EBEAB48CD023D69B1E9DB757#">Unknown licenses</a> that cannot be detected automatically</li>
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6">SolucionesKenko/juno</h2>
	<h3>Version: 1ecd7f8c80f068d5f13c466532abe48230701ee6</h3>
	<p>URL:
	
	<a href="https://github.com/SolucionesKenko/juno.git">https://github.com/SolucionesKenko/juno.git</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/SOLUCIONESKENKO/JUNO.GIT#1ECD7F8C80F068D5F13C466532ABE48230701EE6#">Unknown licenses</a> that cannot be detected automatically</li>
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/CMSIS#4B96CBB174678DCD3CA86E11E1F24BC5F8726DA0">zephyrproject-rtos/cmsis</h2>
	<h3>Version: 4b96cbb174678dcd3ca86e11e1f24bc5f8726da0</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/cmsis">https://github.com/zephyrproject-rtos/cmsis</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/CMSIS#4B96CBB174678DCD3CA86E11E1F24BC5F8726DA0#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717">zephyrproject-rtos/hal_nordic</h2>
	<h3>Version: 5c8d109371ebb740fbef1f440a3b59e488a36717</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/hal_nordic">https://github.com/zephyrproject-rtos/hal_nordic</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/HAL_NORDIC#5C8D109371EBB740FBEF1F440A3B59E488A36717#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/LIBMETAL#A6851BA6DBA8C9E87D00C42F171A822F7A29639B">zephyrproject-rtos/libmetal</h2>
	<h3>Version: a6851ba6dba8c9e87d00c42f171a822f7a29639b</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/libmetal">https://github.com/zephyrproject-rtos/libmetal</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/LIBMETAL#A6851BA6DBA8C9E87D00C42F171A822F7A29639B#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719">zephyrproject-rtos/open-amp</h2>
	<h3>Version: b735edbc739ad59156eb55bb8ce2583d74537719</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/open-amp">https://github.com/zephyrproject-rtos/open-amp</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719#BSD-2-CLAUSE">BSD-2-Clause</a></li>
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/OPEN-AMP#B735EDBC739AD59156EB55BB8CE2583D74537719#BSD-3-CLAUSE">BSD-3-Clause</a></li>
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/SEGGER#B011C45B585E097D95D9CF93EDF4F2E01588D3CD">zephyrproject-rtos/segger</h2>
	<h3>Version: b011c45b585e097d95d9cf93edf4f2e01588d3cd</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/segger">https://github.com/zephyrproject-rtos/segger</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/SEGGER#B011C45B585E097D95D9CF93EDF4F2E01588D3CD#LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">LicenseRef-west-ncs-sbom-BSD-1-Clause-SEGGER</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/TINYCRYPT#1012A3EBEE18C15EDE5EFC8332EE2FC37817670F">zephyrproject-rtos/tinycrypt</h2>
	<h3>Version: 1012a3ebee18c15ede5efc8332ee2fc37817670f</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/tinycrypt">https://github.com/zephyrproject-rtos/tinycrypt</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/TINYCRYPT#1012A3EBEE18C15EDE5EFC8332EE2FC37817670F#LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	
	

	<h2 id="package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/ZCBOR#47F34DD7F5284E8750B5A715DEE7F77C6C5BDC3F">zephyrproject-rtos/zcbor</h2>
	<h3>Version: 47f34dd7f5284e8750b5a715dee7f77c6c5bdc3f</h3>
	<p>URL:
	
	<a href="https://github.com/zephyrproject-rtos/zcbor">https://github.com/zephyrproject-rtos/zcbor</a>
	
	</p>
	<p>The files from this source are covered by the following licenses:</p>
	<ul>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		<li><a href="#package#GIT#HTTPS://GITHUB.COM/ZEPHYRPROJECT-RTOS/ZCBOR#47F34DD7F5284E8750B5A715DEE7F77C6C5BDC3F#APACHE-2.0">Apache-2.0</a></li>
		
		
		
		
		
		
		
		
		
		
		
		
		
		
		
	</ul>
	
	

	<h1>Custom license texts</h1>

	
	<h2 id="text-LICENSEREF-NORDIC-5-CLAUSE">LicenseRef-Nordic-5-Clause</h2>
	<pre>Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
   list of conditions and the following disclaimer.

2. Redistributions in binary form, except as embedded into a Nordic
   Semiconductor ASA integrated circuit in a product or a software update for
   such product, must reproduce the above copyright notice, this list of
   conditions and the following disclaimer in the documentation and/or other
   materials provided with the distribution.

3. Neither the name of Nordic Semiconductor ASA nor the names of its
   contributors may be used to endorse or promote products derived from this
   software without specific prior written permission.

4. This software, with or without modification, must only be used with a
   Nordic Semiconductor ASA integrated circuit.

5. Any software provided in binary form under this license must not be reverse
   engineered, decompiled, modified and/or disassembled.

THIS SOFTWARE IS PROVIDED BY NORDIC SEMICONDUCTOR ASA &#34;AS IS&#34; AND ANY EXPRESS
OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
OF MERCHANTABILITY, NONINFRINGEMENT, AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL NORDIC SEMICONDUCTOR ASA OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
</pre>
	
	<h2 id="text-LICENSEREF-WEST-NCS-SBOM-BSD-1-CLAUSE-SEGGER">LicenseRef-west-ncs-sbom-BSD-1-Clause-SEGGER</h2>
	<pre>Redistribution and use in source and binary forms, with or
without modification, are permitted provided that the following
condition is met:

o Redistributions of source code must retain the above copyright
  notice, this condition and the following disclaimer.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
CONTRIBUTORS &#34;AS IS&#34; AND ANY EXPRESS OR IMPLIED WARRANTIES,
INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL SEGGER Microcontroller BE LIABLE FOR
ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT
OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
DAMAGE.
</pre>
	
	<h2 id="text-LICENSEREF-WEST-NCS-SBOM-BSD-3-CLAUSE-INTEL">LicenseRef-west-ncs-sbom-BSD-3-Clause-Intel</h2>
	<pre>Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

  - Redistributions of source code must retain the above copyright notice,
    this list of conditions and the following disclaimer.

  - Redistributions in binary form must reproduce the above copyright
  notice, this list of conditions and the following disclaimer in the
  documentation and/or other materials provided with the distribution.

  - Neither the name of Intel Corporation nor the names of its contributors
  may be used to endorse or promote products derived from this software
  without specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &#34;AS IS&#34;
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
POSSIBILITY OF SUCH DAMAGE.
</pre>
	
</body>

</html>