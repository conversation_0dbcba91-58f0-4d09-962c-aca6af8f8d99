# BLE Device Documentation

This document describes the Bluetooth Low Energy (BLE) Generic Attribute Profile (GATT) services and characteristics exposed by the Juno device.

**Endianness:** All multi-byte numerical values (e.g., `int32_t`, `uint32_t`) are encoded in **Little Endian** format unless otherwise specified.

## Services Summary

The device exposes the following custom GATT services:

1.  **Heat Control Service (HCS):** Allows controlling and monitoring the heating function.
2.  **TENS Control Service (TCS):** Allows controlling and monitoring the TENS (Transcutaneous Electrical Nerve Stimulation) function.
3.  **Device Control Service (DCS):** Provides general device control functionalities.
4.  **Device Status Service (DSS):** Provides information about the device's status and potential faults.

---

## 1. Heat Control Service (HCS)

*Base UUID: `0x23289bb0-670c-4635-8b38-e1ab58c0e9c4` (Example, verify actual)*

### Characteristics

| Characteristic Name | UUID Suffix | Data Format (Read/Indicate) | Data Format (Write) | Permissions     | Notes |
|---------------------|-------------|-----------------------------|---------------------|-----------------|-------|
| Target Heat Level   | `0x23289bb1`| `uint8_t`                   | `uint8_t`           | Read, Write | Target heat level (`heat_control_target_e`) |
| Measured Temperature| `0x23289bb2`| `int32_t`                   | N/A                 | Read, Indicate  | Temperature in Celsius × 100 (e.g., `4250` = `42.50 °C`) |
| Active Heat Level   | `0x23289bb3`| `uint8_t`                   | N/A                 | Read, Indicate  | Current active heat level (`heat_control_target_e`) |

### Enumerations (HCS)

#### `heat_control_target_e` (Target/Active Heat Level)

| Value | Name                 | Description  |
|-------|----------------------|--------------|
| `0x00`| `HEAT_CONTROL_LEVEL_0` | Heat Off / Min |
| `0x01`| `HEAT_CONTROL_LEVEL_1` | Heat Level 1 |
| `0x02`| `HEAT_CONTROL_LEVEL_2` | Heat Level 2 |
| `0x03`| `HEAT_CONTROL_LEVEL_3` | Heat Level 3 |

*(Refer to `heat_control.h` for exact enum details)*

---

## 2. TENS Control Service (TCS)

Base UUID: `0x23289aa0-670c-4635-8b38-e1ab58c0e9c4`

### Characteristics

| Characteristic Name       | UUID Suffix | Data Format (Read/Indicate) | Data Format (Write) | Permissions          | Notes |
|---------------------------|-------------|-----------------------------|---------------------|----------------------|-------|
| **Mode**                  | `0x23289aa1`| `uint8_t`                   | `uint8_t`           | Read, Write, Indicate| Represents TENS mode (see `tens_control_mode` enum below) |
| **Selected TENS Level**   | `0x23289aa2`| `uint8_t`                   | `uint8_t`           | Read, Write          | Read: Current target TENS intensity level (`tens_control_intensity`).<br>Write: Command to adjust TENS level: `0x01` (Increase), `0x02` (Decrease). See `ble_module_message_type_e`. |
| **Active TENS Level**     | `0x23289aa3`| `uint8_t`                   | N/A                 | Read, Indicate       | Active TENS intensity level that has been reached (`tens_control_intensity`). |
| **Measured Intensity**    | `0x23289aa4`| `uint32_t`                  | N/A                 | Read, Indicate       | Measured TENS current in microamperes (uA). |


### Enumerations (TCS)

#### `tens_control_mode` (TENS Mode)

*As defined in `events/tens_module_event.h`*

| Value | Name                              | Description           |
|-------|-----------------------------------|-----------------------|
| `0x00`| `TENS_CONTROL_MODE_IDLE`          | Idle / Off            |
| `0x01`| `TENS_CONTROL_MODE_LOW_FREQUENCY` | Low Frequency Mode    |
| `0x02`| `TENS_CONTROL_MODE_HIGH_FREQUENCY`| High Frequency Mode   |
| `0x03`| `TENS_CONTROL_MODE_BURST_MODE`    | Burst Mode            |

#### `tens_control_intensity` (Intensity Level)

*As defined in `events/tens_module_event.h`. Represents both target and active levels.*

| Value  | Name                | Approx. Current (uA, from `tens_control.c` map) |
|--------|---------------------|-----------------------------------------------|
| `0x00` | `TENS_INTENSITY_0`  | 0                                             |
| `0x01` | `TENS_INTENSITY_1`  | 12000                                         |
| `0x02` | `TENS_INTENSITY_2`  | 15000                                         |
| `0x03` | `TENS_INTENSITY_3`  | 18000                                         |
| `0x04` | `TENS_INTENSITY_4`  | 21000                                         |
| `0x05` | `TENS_INTENSITY_5`  | 24000                                         |
| `0x06` | `TENS_INTENSITY_6`  | 29000                                         |
| `0x07` | `TENS_INTENSITY_7`  | 34000                                         |
| `0x08` | `TENS_INTENSITY_8`  | 39000                                         |
| `0x09` | `TENS_INTENSITY_9`  | 44000                                         |
| `0x0A` | `TENS_INTENSITY_10` | 49000                                         |

#### `ble_module_message_type_e` values for Selected TENS Level Characteristic Write

*As defined in `events/ble_module_event.h` (subset used for TENS level adjustment)*

| Value  | Name                               | Description         |
|--------|------------------------------------|---------------------|
| `1` | `BT_TENS_LEVEL_ADJUST_INCREASE`    | Increase TENS Level |
| `-1` | `BT_TENS_LEVEL_ADJUST_DECREASE`    | Decrease TENS Level |


## 3. Device Control Service (DCS)

*Base UUID: `0xA5E1DC00-5313-45C9-A0C1-************`*

### Characteristics

| Characteristic Name | UUID Suffix | Data Format (Read/Indicate) | Data Format (Write) | Permissions | Notes |
|---------------------|-------------|-----------------------------|---------------------|-------------|-------|
| Power State         | `0xA5E1DC01`| N/A                         | `uint8_t`           | Write       | Write `0x00` to turn the device OFF. Other values are rejected. |
| Therapy State       | `0xA5E1DC02`| `uint8_t`                   | `uint8_t`           | Read, Write | Read: `0x00` (Paused), `0x01` (Active/Resumed).<br>Write: `0x00` to Pause Therapy, `0x01` to Start/Resume Therapy. Other values rejected. |

## 4. Device Status Service (DSS)

*Base UUID: `0xB5E1DC00-5313-45C9-A0C1-************`*

### Characteristics

| Characteristic Name | UUID Suffix | Data Format (Read/Indicate) | Data Format (Write) | Permissions | Notes |
|---------------------|-------------|-----------------------------|---------------------|-------------|-------|
| Status              | `0xB5E1DC01`| `uint32_t`                  | N/A                 | Read        | Provides device status or fault codes (4 bytes, Little Endian). The specific meaning of the `uint32_t` value is determined by the `status_read_cb` implementation in the firmware. It might be a bitmask or an enum. |


---

## 5. Battery Service (BAS)

*Standard BLE Service UUID: `0x180F`*

This service exposes the battery level of the device.

### Characteristics

| Characteristic Name | Standard UUID | Data Format (Read/Notify) | Data Format (Write) | Permissions     | Notes |
|---------------------|---------------|-----------------------------|---------------------|-----------------|-------|
| Battery Level       | `0x2A19`      | `uint8_t`                   | N/A                 | Read, Notify    | Battery level as a percentage from 0% to 100%. |

---

## Testing BLE Communication

After programming the firmware to your device:

1.  Reset the device. Observe boot logs in the terminal.
2.  Open a BLE scanning tool on your smartphone or computer (e.g., `nRF Connect for Mobile`).
3.  Scan for BLE devices and look for the device advertising with the name specified in `CONFIG_BT_DEVICE_NAME` (e.g., "Juno").
4.  Connect to the device. Pairing might be required depending on security settings. If passkey pairing is enabled, the passkey will be printed on the terminal.
5.  Once connected, discover the GATT services and characteristics.
6.  Interact with the characteristics as described above (e.g., write to Mode, Selected TENS Level; read Active TENS Level, Measured Intensity).
7.  Observe the terminal output for log messages corresponding to the BLE operations and device state changes. 