part of 'period_tracking_watcher_bloc.dart';

@freezed
class PeriodTrackingWatcherState with _$PeriodTrackingWatcherState {
  const factory PeriodTrackingWatcherState.initial() = _Initial;
  const factory PeriodTrackingWatcherState.loading() = _Loading;
  const factory PeriodTrackingWatcherState.loadSuccess(
      Map<String, Map<String, PeriodTrackingModel>> yearData) = _LoadSuccess;
  const factory PeriodTrackingWatcherState.loadFailure(
      PeriodTrackingFailure failure) = _LoadFailure;
}
