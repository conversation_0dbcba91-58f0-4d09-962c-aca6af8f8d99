import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../domain/facade/period_tracking_facade.dart';
import '../../domain/failure/period_tracking_failure.dart';

part 'manage_period_tracking_event.dart';
part 'manage_period_tracking_state.dart';
part 'manage_period_tracking_bloc.freezed.dart';

@injectable
class ManagePeriodTrackingBloc
    extends Bloc<ManagePeriodTrackingEvent, ManagePeriodTrackingState> {
  final PeriodTrackingFacade _periodTrackingFacade;

  ManagePeriodTrackingBloc(this._periodTrackingFacade)
      : super(const ManagePeriodTrackingState.initial()) {
    on<_SelectPeriodDates>(_onSelectPeriodDates);
    on<_DeselectPeriodDates>(_onDeselectPeriodDates);
    on<_CalculateOvulationDates>(_onCalculateOvulationDates);
    on<_RemoveOvulationDatesForCycles>(_onRemoveOvulationDatesForCycles);
    on<_DateSelected>(_onDateSelected);
    on<_DateDeselected>(_onDateDeselected);
    on<_ClearSelection>(_onClearSelection);
  }

  Future<void> _onSelectPeriodDates(
    _SelectPeriodDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result =
        await _periodTrackingFacade.selectPeriodDates(event.selectedDates);

    result.mapBoth(
      onLeft: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onRight: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onDeselectPeriodDates(
    _DeselectPeriodDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result =
        await _periodTrackingFacade.deselectPeriodDates(event.datesToDeselect);

    result.mapBoth(
      onLeft: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onRight: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onCalculateOvulationDates(
    _CalculateOvulationDates event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result = await _periodTrackingFacade
        .calculateAndSaveOvulationDates(event.periodDates);

    result.mapBoth(
      onLeft: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onRight: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onRemoveOvulationDatesForCycles(
    _RemoveOvulationDatesForCycles event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    emit(const ManagePeriodTrackingState.loading());

    final result = await _periodTrackingFacade
        .removeOvulationDatesForCycles(event.periodDatesToRemove);

    result.mapBoth(
      onLeft: (failure) => emit(ManagePeriodTrackingState.failure(failure)),
      onRight: (_) => emit(const ManagePeriodTrackingState.success()),
    );
  }

  Future<void> _onDateSelected(
    _DateSelected event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // This is handled locally in the UI, no need to emit state changes
    // Just keeping the event for potential future use
  }

  Future<void> _onDateDeselected(
    _DateDeselected event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // This is handled locally in the UI, no need to emit state changes
    // Just keeping the event for potential future use
  }

  Future<void> _onClearSelection(
    _ClearSelection event,
    Emitter<ManagePeriodTrackingState> emit,
  ) async {
    // This is handled locally in the UI, no need to emit state changes
    // Just keeping the event for potential future use
  }
}
