import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:timezone/timezone.dart' as tz;

import '../domain/facade/period_reminder_facade.dart';
import '../domain/failure/period_tracking_failure.dart';
import '../domain/model/period_reminder_settings.dart';
import '../domain/model/health_data.dart';

@LazySingleton(as: PeriodReminderFacade)
class PeriodReminderRepository implements PeriodReminderFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final ScheduledNotificationsFacade _notificationsFacade;

  PeriodReminderRepository(this._notificationsFacade);

  // Local storage key
  static const String _reminderSettingsKey = 'period_reminder_settings';

  @override
  Future<Either<PeriodTrackingFailure, PeriodReminderSettings>>
      getReminderSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_reminderSettingsKey);

      if (settingsJson != null) {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        final settings = PeriodReminderSettings.fromJson(settingsMap);
        return Right(settings);
      } else {
        // Return default settings if none exist
        return Right(PeriodReminderSettings.empty());
      }
    } catch (e) {
      print('Error loading reminder settings: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> saveReminderSettings(
      PeriodReminderSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = jsonEncode(settings.toJson());
      await prefs.setString(_reminderSettingsKey, settingsJson);

      // Also save to Firestore for cross-device sync
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .update({'reminderSettings': settings.toJson()});
      }

      // Schedule notifications based on the new settings
      await scheduleNotificationsForSettings(settings);

      return const Right(unit);
    } catch (e) {
      print('Error saving reminder settings: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>> scheduleNotificationsForSettings(
      PeriodReminderSettings settings) async {
    try {
      print('Scheduling notifications for settings: ${settings.toJson()}');

      // Cancel existing notifications first
      await cancelAllPeriodNotifications();

      if (!settings.isPeriodReminderEnabled &&
          !settings.isOvulationReminderEnabled) {
        print('No reminders enabled, skipping notification scheduling');
        return const Right(unit);
      }

      // Get future period and ovulation dates
      final futureDates = await _calculateFutureNotificationDates(settings);
      if (futureDates.isEmpty) {
        print('No future dates available for notifications');
        return const Right(unit);
      }

      // Schedule period notifications
      if (settings.isPeriodReminderEnabled && futureDates['periods'] != null) {
        await _schedulePeriodNotifications(
            futureDates['periods']!, settings.periodReminderDaysBefore);
      }

      // Schedule ovulation notifications
      if (settings.isOvulationReminderEnabled &&
          futureDates['ovulations'] != null) {
        await _scheduleOvulationNotifications(
            futureDates['ovulations']!, settings.ovulationReminderDaysBefore);
      }

      print('Successfully scheduled notifications');
      return const Right(unit);
    } catch (e) {
      print('Error scheduling notifications: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      cancelAllPeriodNotifications() async {
    try {
      print('Canceling all period notifications');

      // Cancel period notification group
      await _notificationsFacade.disableNotificationGroup('period_reminders');

      // Cancel ovulation notification group
      await _notificationsFacade
          .disableNotificationGroup('ovulation_reminders');

      // Clear the stored notification IDs
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('period_notification_ids');

      print('Canceled all period and ovulation notifications');
      return const Right(unit);
    } catch (e) {
      print('Error canceling notifications: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<Either<PeriodTrackingFailure, Unit>>
      initializePeriodReminders() async {
    try {
      print('Initializing period reminders system...');

      // Check if we need to sync settings from Firestore first
      await _syncReminderSettingsFromFirestore();

      // Load reminder settings from local storage and Firestore
      final settingsResult = await getReminderSettings();

      final settings = settingsResult.getOrElse((failure) {
        print(
            'Failed to load reminder settings during initialization: $failure');
        return PeriodReminderSettings.empty();
      });

      print('Loaded reminder settings: ${settings.toJson()}');

      // Schedule notifications based on current settings
      if (settings.isPeriodReminderEnabled ||
          settings.isOvulationReminderEnabled) {
        print('Scheduling notifications during initialization...');
        final scheduleResult = await scheduleNotificationsForSettings(settings);

        scheduleResult.mapBoth(
          onLeft: (failure) {
            print(
                'Failed to schedule notifications during initialization: $failure');
          },
          onRight: (_) {
            print('Period reminders system initialized successfully');
          },
        );

        return scheduleResult;
      } else {
        print('No reminders enabled, initialization complete');
        return const Right(unit);
      }
    } catch (e) {
      print('Error initializing period reminders: $e');
      return const Left(PeriodTrackingFailure.unexpected());
    }
  }

  @override
  Future<void> rescheduleNotificationsAfterPeriodUpdate() async {
    try {
      print('Rescheduling notifications after period data update');

      // Get current reminder settings
      final settingsResult = await getReminderSettings();

      settingsResult.mapBoth(
        onLeft: (failure) {
          print('Failed to get reminder settings for rescheduling: $failure');
        },
        onRight: (settings) async {
          // Only reschedule if reminders are enabled
          if (settings.isPeriodReminderEnabled ||
              settings.isOvulationReminderEnabled) {
            print(
                'Rescheduling notifications with settings: ${settings.toJson()}');
            await scheduleNotificationsForSettings(settings);
          } else {
            print('No reminders enabled, skipping notification rescheduling');
          }
        },
      );
    } catch (e) {
      print('Error rescheduling notifications: $e');
    }
  }

  // Helper method to calculate future notification dates
  Future<Map<String, DateTime?>> _calculateFutureNotificationDates(
      PeriodReminderSettings settings) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        print('No user logged in, cannot calculate future dates');
        throw Exception('User not logged in');
      }
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final healthDataDoc =
          HealthDataModel.fromJson(userDoc.data()?['healthData']);
      print(
          'Health data loaded: lastPeriodDate=${healthDataDoc.nextPeriodStartDate}, cycleLength=${healthDataDoc.cycleLength}, periodLength=${healthDataDoc.periodLength}');

      if (healthDataDoc.nextPeriodStartDate == null ||
          healthDataDoc.nextOvulationDate == null) {
        throw Exception('Future dates do not exist');
      }

      return {
        'periods': healthDataDoc.nextPeriodStartDate,
        'ovulations': healthDataDoc.nextOvulationDate,
      };
    } catch (e) {
      print('Error calculating future dates: $e');
      return {'periods': null, 'ovulations': null};
    }
  }

  // Helper method to schedule period notifications
  Future<void> _schedulePeriodNotifications(
      DateTime? periodDate, int daysBefore) async {
    try {
      if (periodDate == null) {
        print('No period dates to schedule notifications for');
        return;
      }

      final notificationDate = periodDate.subtract(Duration(days: daysBefore));

      // Only schedule if notification date is in the future
      if (notificationDate.isAfter(DateTime.now())) {
        final notificationId =
            'period_${periodDate.millisecondsSinceEpoch}_$daysBefore';

        // Convert to timezone-aware datetime
        final tzDateTime = tz.TZDateTime.from(
          DateTime(notificationDate.year, notificationDate.month,
              notificationDate.day, 9, 0), // 9 AM
          tz.local,
        );

        // Schedule single notification
        final result = await _notificationsFacade.scheduleSingleNotification(
          body: daysBefore == 1
              ? 'Your period is expected to start tomorrow. Be prepared!'
              : 'Your period is expected to start in $daysBefore days. Be prepared!',
          dateTime: tzDateTime,
          notificationId: notificationId,
          notificationType: 'period_reminder',
          title: 'Period Reminder',
          payload: 'period_reminder:${periodDate.toIso8601String()}',
          isForeground: true,
        );

        result.mapBoth(
          onLeft: (failure) =>
              print('Failed to schedule period notification: $failure'),
          onRight: (_) => print(
              'Successfully scheduled period notification for ${tzDateTime.toIso8601String()}'),
        );
      }
    } catch (e) {
      print('Error scheduling period notifications: $e');
    }
  }

  // Helper method to schedule ovulation notifications
  Future<void> _scheduleOvulationNotifications(
      DateTime? ovulationDate, int daysBefore) async {
    try {
      if (ovulationDate == null) {
        print('No ovulation dates to schedule notifications for');
        return;
      }

      final notificationDate =
          ovulationDate.subtract(Duration(days: daysBefore + 2));

      // Only schedule if notification date is in the future
      if (notificationDate.isAfter(DateTime.now())) {
        final notificationId =
            'ovulation_${ovulationDate.millisecondsSinceEpoch}_$daysBefore';

        // Convert to timezone-aware datetime
        final tzDateTime = tz.TZDateTime.from(
          DateTime(notificationDate.year, notificationDate.month,
              notificationDate.day, 9, 0), // 9 AM
          tz.local,
        );

        // Schedule single notification
        final result = await _notificationsFacade.scheduleSingleNotification(
          body: daysBefore == 1
              ? 'Your ovulation window starts tomorrow. This is your fertile period!'
              : 'Your ovulation window starts in $daysBefore days. This is your fertile period!',
          dateTime: tzDateTime,
          notificationId: notificationId,
          title: 'Ovulation Reminder',
          notificationType: 'ovulation_reminder',
          payload: 'ovulation_reminder:${ovulationDate.toIso8601String()}',
          isForeground: true,
        );

        result.mapBoth(
          onLeft: (failure) =>
              print('Failed to schedule ovulation notification: $failure'),
          onRight: (_) => print(
              'Successfully scheduled ovulation notification for ${tzDateTime.toIso8601String()}'),
        );
      }
    } catch (e) {
      print('Error scheduling ovulation notifications: $e');
    }
  }

  // Helper method to sync reminder settings from Firestore
  Future<void> _syncReminderSettingsFromFirestore() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) return;

      final userDoc = await _firestore.collection('users').doc(user.uid).get();

      if (userDoc.exists &&
          userDoc.data()?.containsKey('reminderSettings') == true) {
        final firestoreSettings =
            userDoc.data()!['reminderSettings'] as Map<String, dynamic>;
        final settings = PeriodReminderSettings.fromJson(firestoreSettings);

        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        final settingsJson = jsonEncode(settings.toJson());
        await prefs.setString(_reminderSettingsKey, settingsJson);

        print('Synced reminder settings from Firestore to local storage');
      }
    } catch (e) {
      print('Error syncing reminder settings from Firestore: $e');
    }
  }
}
